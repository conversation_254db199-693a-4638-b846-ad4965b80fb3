package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// EnhancedExecutionEngine 增强执行引擎
type EnhancedExecutionEngine struct {
	intentRouter    *IntentRouter
	enhancedEngine  *EnhancedIntentEngine
	executionQueue  *ExecutionQueue
	resultProcessor *ResultProcessor
	logger          *logrus.Logger
	config          *ExecutionConfig
	metrics         *ExecutionMetrics
}

// ExecutionConfig 执行配置
type ExecutionConfig struct {
	MaxConcurrentExecutions int           `json:"max_concurrent_executions"`
	DefaultTimeout          time.Duration `json:"default_timeout"`
	RetryAttempts           int           `json:"retry_attempts"`
	RetryDelay              time.Duration `json:"retry_delay"`
	EnableAutoConfirm       bool          `json:"enable_auto_confirm"`
	ConfidenceThreshold     float64       `json:"confidence_threshold"`
}

// ExecutionMetrics 执行指标
type ExecutionMetrics struct {
	TotalExecutions      int64                    `json:"total_executions"`
	SuccessfulExecutions int64                    `json:"successful_executions"`
	FailedExecutions     int64                    `json:"failed_executions"`
	AverageExecutionTime float64                  `json:"average_execution_time_ms"`
	ExecutionsByType     map[string]int64         `json:"executions_by_type"`
	LastUpdated          time.Time                `json:"last_updated"`
	mutex                sync.RWMutex             `json:"-"`
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Message     string                 `json:"message"`
	Context     map[string]interface{} `json:"context"`
	Priority    int                    `json:"priority"`
	Timeout     time.Duration          `json:"timeout"`
	RequestedAt time.Time              `json:"requested_at"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	RequestID       string                 `json:"request_id"`
	SessionID       string                 `json:"session_id"`
	Success         bool                   `json:"success"`
	IntentResult    *EnhancedIntentResult  `json:"intent_result"`
	HandleResult    *IntentHandleResult    `json:"handle_result"`
	ExecutionTime   time.Duration          `json:"execution_time"`
	Error           string                 `json:"error,omitempty"`
	Warnings        []string               `json:"warnings"`
	Recommendations []string               `json:"recommendations"`
	CompletedAt     time.Time              `json:"completed_at"`
}

// ExecutionQueue 执行队列
type ExecutionQueue struct {
	maxWorkers int
	taskChan   chan *ExecutionTask
	logger     *logrus.Logger
	workers    []*ExecutionWorker
	mutex      sync.RWMutex
}

// ExecutionTask 执行任务
type ExecutionTask struct {
	ID       string
	Request  *ExecutionRequest
	ResultCh chan *ExecutionResult
	Context  context.Context
}

// ExecutionWorker 执行工作者
type ExecutionWorker struct {
	ID       int
	TaskChan chan *ExecutionTask
	QuitChan chan bool
	Logger   *logrus.Logger
}

// ResultProcessor 结果处理器
type ResultProcessor struct {
	logger *logrus.Logger
}

// NewEnhancedExecutionEngine 创建增强执行引擎
func NewEnhancedExecutionEngine(
	intentRouter *IntentRouter,
	enhancedEngine *EnhancedIntentEngine,
	logger *logrus.Logger,
) *EnhancedExecutionEngine {
	config := &ExecutionConfig{
		MaxConcurrentExecutions: 10,
		DefaultTimeout:          30 * time.Second,
		RetryAttempts:           3,
		RetryDelay:              1 * time.Second,
		EnableAutoConfirm:       false,
		ConfidenceThreshold:     0.8,
	}

	metrics := &ExecutionMetrics{
		ExecutionsByType: make(map[string]int64),
		LastUpdated:      time.Now(),
	}

	return &EnhancedExecutionEngine{
		intentRouter:    intentRouter,
		enhancedEngine:  enhancedEngine,
		executionQueue:  NewExecutionQueue(config.MaxConcurrentExecutions, logger),
		resultProcessor: NewResultProcessor(logger),
		logger:          logger,
		config:          config,
		metrics:         metrics,
	}
}

// NewExecutionQueue 创建执行队列
func NewExecutionQueue(maxWorkers int, logger *logrus.Logger) *ExecutionQueue {
	eq := &ExecutionQueue{
		maxWorkers: maxWorkers,
		taskChan:   make(chan *ExecutionTask, maxWorkers*2),
		logger:     logger,
		workers:    make([]*ExecutionWorker, maxWorkers),
	}

	// 启动工作者
	for i := 0; i < maxWorkers; i++ {
		worker := &ExecutionWorker{
			ID:       i,
			TaskChan: eq.taskChan,
			QuitChan: make(chan bool),
			Logger:   logger,
		}
		eq.workers[i] = worker
		go worker.Start()
	}

	return eq
}

// Start 启动工作者
func (ew *ExecutionWorker) Start() {
	ew.Logger.WithField("worker_id", ew.ID).Info("Execution worker started")
	
	for {
		select {
		case task := <-ew.TaskChan:
			ew.processTask(task)
		case <-ew.QuitChan:
			ew.Logger.WithField("worker_id", ew.ID).Info("Execution worker stopped")
			return
		}
	}
}

// processTask 处理任务
func (ew *ExecutionWorker) processTask(task *ExecutionTask) {
	ew.Logger.WithFields(logrus.Fields{
		"worker_id": ew.ID,
		"task_id":   task.ID,
	}).Info("Processing execution task")

	// 这里应该实际执行任务
	// 暂时返回成功结果
	result := &ExecutionResult{
		RequestID:   task.ID,
		SessionID:   task.Request.SessionID,
		Success:     true,
		CompletedAt: time.Now(),
	}

	task.ResultCh <- result
}

// NewResultProcessor 创建结果处理器
func NewResultProcessor(logger *logrus.Logger) *ResultProcessor {
	return &ResultProcessor{logger: logger}
}

// ProcessResult 处理结果
func (rp *ResultProcessor) ProcessResult(handleResult *IntentHandleResult, intentResult *EnhancedIntentResult) *IntentHandleResult {
	// 增强处理结果
	if handleResult.Data == nil {
		handleResult.Data = make(map[string]interface{})
	}

	// 添加意图信息
	handleResult.Data["intent_type"] = intentResult.Type
	handleResult.Data["confidence"] = intentResult.Confidence
	handleResult.Data["risk_level"] = intentResult.RiskLevel

	// 优化消息格式
	if handleResult.Success {
		handleResult.Message = fmt.Sprintf("✅ %s", handleResult.Message)
	} else {
		handleResult.Message = fmt.Sprintf("❌ %s", handleResult.Message)
	}

	return handleResult
}

// ProcessMessage 处理消息（增强执行）
func (eee *EnhancedExecutionEngine) ProcessMessage(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	start := time.Now()
	requestID := fmt.Sprintf("exec_%d_%s", time.Now().UnixNano(), req.SessionID)

	eee.logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("Enhanced execution engine processing message")

	// 更新指标
	eee.updateMetrics("total", 0, false, true)

	// 第一步：意图识别
	intentResult, err := eee.enhancedEngine.RecognizeIntent(ctx, req.SessionID, req.Message)
	if err != nil {
		eee.updateMetrics("intent_recognition_failed", time.Since(start), false, false)
		return &ExecutionResult{
			RequestID:   requestID,
			SessionID:   req.SessionID,
			Success:     false,
			Error:       fmt.Sprintf("意图识别失败: %v", err),
			CompletedAt: time.Now(),
		}, err
	}

	// 检查置信度
	if intentResult.Confidence < eee.config.ConfidenceThreshold {
		eee.logger.WithFields(logrus.Fields{
			"confidence": intentResult.Confidence,
			"threshold":  eee.config.ConfidenceThreshold,
		}).Warn("Low confidence intent recognition")

		return &ExecutionResult{
			RequestID:    requestID,
			SessionID:    req.SessionID,
			Success:      false,
			IntentResult: intentResult,
			Error:        "意图识别置信度过低，请提供更明确的指令",
			Warnings:     []string{fmt.Sprintf("置信度: %.2f, 阈值: %.2f", intentResult.Confidence, eee.config.ConfidenceThreshold)},
			Recommendations: []string{
				"请使用更具体的描述",
				"可以参考帮助文档中的示例",
			},
			CompletedAt: time.Now(),
		}, nil
	}

	// 第二步：风险评估和确认
	if intentResult.RequiresConfirm && !eee.config.EnableAutoConfirm {
		return eee.createConfirmationResult(requestID, req, intentResult), nil
	}

	// 第三步：执行意图处理
	handleResult, err := eee.intentRouter.RouteIntent(ctx, intentResult, req.SessionID)
	if err != nil {
		eee.updateMetrics(intentResult.Type, time.Since(start), false, false)
		return &ExecutionResult{
			RequestID:    requestID,
			SessionID:    req.SessionID,
			Success:      false,
			IntentResult: intentResult,
			Error:        fmt.Sprintf("意图处理失败: %v", err),
			CompletedAt:  time.Now(),
		}, err
	}

	// 第四步：结果处理和优化
	processedResult := eee.resultProcessor.ProcessResult(handleResult, intentResult)

	// 更新指标
	eee.updateMetrics(intentResult.Type, time.Since(start), processedResult.Success, false)

	return &ExecutionResult{
		RequestID:       requestID,
		SessionID:       req.SessionID,
		Success:         processedResult.Success,
		IntentResult:    intentResult,
		HandleResult:    processedResult,
		ExecutionTime:   time.Since(start),
		Warnings:        eee.generateWarnings(intentResult, processedResult),
		Recommendations: eee.generateRecommendations(intentResult, processedResult),
		CompletedAt:     time.Now(),
	}, nil
}

// createConfirmationResult 创建确认结果
func (eee *EnhancedExecutionEngine) createConfirmationResult(requestID string, req *ExecutionRequest, intentResult *EnhancedIntentResult) *ExecutionResult {
	return &ExecutionResult{
		RequestID:    requestID,
		SessionID:    req.SessionID,
		Success:      false,
		IntentResult: intentResult,
		HandleResult: &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("⚠️ 检测到%s风险操作，需要确认", intentResult.RiskLevel),
			Data: map[string]interface{}{
				"requires_confirmation": true,
				"risk_level":           intentResult.RiskLevel,
				"operation_summary":    eee.generateOperationSummary(intentResult),
			},
			NextSteps: []string{
				"回复'确认'继续执行操作",
				"回复'取消'放弃操作",
				"回复'详情'查看操作详细信息",
			},
		},
		Warnings: []string{
			fmt.Sprintf("此操作风险等级: %s", intentResult.RiskLevel),
			"请仔细确认操作内容后再继续",
		},
		CompletedAt: time.Now(),
	}
}

// generateOperationSummary 生成操作摘要
func (eee *EnhancedExecutionEngine) generateOperationSummary(intentResult *EnhancedIntentResult) string {
	switch intentResult.Type {
	case "host_management":
		if op, exists := intentResult.Parameters["operation"]; exists {
			return fmt.Sprintf("主机管理操作: %s", op)
		}
		return "主机管理操作"
	case "service_management":
		if service, exists := intentResult.Parameters["service"]; exists {
			return fmt.Sprintf("服务管理操作: %s", service)
		}
		return "服务管理操作"
	case "automation_workflow":
		return "自动化工作流执行"
	default:
		return fmt.Sprintf("%s 操作", intentResult.Type)
	}
}

// generateWarnings 生成警告信息
func (eee *EnhancedExecutionEngine) generateWarnings(intentResult *EnhancedIntentResult, handleResult *IntentHandleResult) []string {
	warnings := []string{}

	// 基于风险等级的警告
	switch intentResult.RiskLevel {
	case "high":
		warnings = append(warnings, "⚠️ 高风险操作，请谨慎执行")
	case "medium":
		warnings = append(warnings, "⚠️ 中等风险操作，建议在测试环境先验证")
	}

	// 基于置信度的警告
	if intentResult.Confidence < 0.9 {
		warnings = append(warnings, fmt.Sprintf("⚠️ 意图识别置信度较低 (%.2f)", intentResult.Confidence))
	}

	// 基于处理结果的警告
	if !handleResult.Success && len(handleResult.Warnings) > 0 {
		warnings = append(warnings, handleResult.Warnings...)
	}

	return warnings
}

// generateRecommendations 生成建议
func (eee *EnhancedExecutionEngine) generateRecommendations(intentResult *EnhancedIntentResult, handleResult *IntentHandleResult) []string {
	recommendations := []string{}

	// 基于意图类型的建议
	switch intentResult.Type {
	case "host_management":
		recommendations = append(recommendations, "建议定期备份主机配置信息")
	case "troubleshooting":
		recommendations = append(recommendations, "建议保存诊断结果用于后续分析")
	case "automation_workflow":
		recommendations = append(recommendations, "建议设置监控和告警机制")
	}

	// 基于处理结果的建议
	if handleResult.Success && len(handleResult.NextSteps) > 0 {
		recommendations = append(recommendations, handleResult.NextSteps...)
	}

	return recommendations
}

// updateMetrics 更新指标
func (eee *EnhancedExecutionEngine) updateMetrics(executionType string, duration time.Duration, success bool, isTotal bool) {
	eee.metrics.mutex.Lock()
	defer eee.metrics.mutex.Unlock()

	if isTotal {
		eee.metrics.TotalExecutions++
	} else {
		if success {
			eee.metrics.SuccessfulExecutions++
		} else {
			eee.metrics.FailedExecutions++
		}

		// 更新平均执行时间
		totalExecs := eee.metrics.SuccessfulExecutions + eee.metrics.FailedExecutions
		if totalExecs > 0 {
			currentAvg := eee.metrics.AverageExecutionTime
			newTime := float64(duration.Milliseconds())
			eee.metrics.AverageExecutionTime = (currentAvg*float64(totalExecs-1) + newTime) / float64(totalExecs)
		}

		// 更新按类型统计
		eee.metrics.ExecutionsByType[executionType]++
	}

	eee.metrics.LastUpdated = time.Now()
}

// GetMetrics 获取执行指标
func (eee *EnhancedExecutionEngine) GetMetrics() *ExecutionMetrics {
	eee.metrics.mutex.RLock()
	defer eee.metrics.mutex.RUnlock()

	// 创建副本避免并发问题
	metricsCopy := &ExecutionMetrics{
		TotalExecutions:      eee.metrics.TotalExecutions,
		SuccessfulExecutions: eee.metrics.SuccessfulExecutions,
		FailedExecutions:     eee.metrics.FailedExecutions,
		AverageExecutionTime: eee.metrics.AverageExecutionTime,
		ExecutionsByType:     make(map[string]int64),
		LastUpdated:          eee.metrics.LastUpdated,
	}

	for k, v := range eee.metrics.ExecutionsByType {
		metricsCopy.ExecutionsByType[k] = v
	}

	return metricsCopy
}
