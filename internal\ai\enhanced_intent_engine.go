package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// EnhancedIntentEngine 增强的意图识别引擎
type EnhancedIntentEngine struct {
	deepseekService interface{} // 简化为interface{}
	contextManager  *ContextManager
	logger          *logrus.Logger
	config          *EnhancedIntentConfig
}

// EnhancedIntentConfig 增强意图识别配置
type EnhancedIntentConfig struct {
	ConfidenceThreshold   float64 `json:"confidence_threshold"`
	ContextBoostEnabled   bool    `json:"context_boost_enabled"`
	RiskAssessmentEnabled bool    `json:"risk_assessment_enabled"`
	SmartParameterEnabled bool    `json:"smart_parameter_enabled"`
	MultiTurnEnabled      bool    `json:"multi_turn_enabled"`
	MaxRetries            int     `json:"max_retries"`
	TimeoutSeconds        int     `json:"timeout_seconds"`
}

// EnhancedIntentResult 增强的意图识别结果
type EnhancedIntentResult struct {
	Type            string                 `json:"type"`
	Confidence      float64                `json:"confidence"`
	RiskLevel       string                 `json:"risk_level"`
	Parameters      map[string]interface{} `json:"parameters"`
	Context         map[string]interface{} `json:"context"`
	Suggestions     []string               `json:"suggestions"`
	RequiresConfirm bool                   `json:"requires_confirm"`
	ExecutionPlan   *EnhancedExecutionPlan `json:"execution_plan,omitempty"`
	Timestamp       time.Time              `json:"timestamp"`
}

// EnhancedExecutionPlan 增强执行计划
type EnhancedExecutionPlan struct {
	Steps         []EnhancedExecutionStep `json:"steps"`
	EstimatedTime int                     `json:"estimated_time_seconds"`
	Prerequisites []string                `json:"prerequisites"`
	RollbackPlan  []string                `json:"rollback_plan"`
}

// EnhancedExecutionStep 增强执行步骤
type EnhancedExecutionStep struct {
	ID          string                 `json:"id"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Parameters  map[string]interface{} `json:"parameters"`
	RiskLevel   string                 `json:"risk_level"`
	Timeout     int                    `json:"timeout_seconds"`
}

// ContextHistoryItem 上下文历史项
type ContextHistoryItem struct {
	Intent    string    `json:"intent"`
	Summary   string    `json:"summary"`
	Timestamp time.Time `json:"timestamp"`
}

// NewEnhancedIntentEngine 创建增强意图识别引擎
func NewEnhancedIntentEngine(
	deepseekService interface{}, // 简化为interface{}
	contextManager *ContextManager,
	logger *logrus.Logger,
) *EnhancedIntentEngine {
	config := &EnhancedIntentConfig{
		ConfidenceThreshold:   0.8,
		ContextBoostEnabled:   true,
		RiskAssessmentEnabled: true,
		SmartParameterEnabled: true,
		MultiTurnEnabled:      true,
		MaxRetries:            3,
		TimeoutSeconds:        30,
	}

	return &EnhancedIntentEngine{
		deepseekService: deepseekService,
		contextManager:  contextManager,
		logger:          logger,
		config:          config,
	}
}

// RecognizeIntent 识别意图（增强版）
func (eie *EnhancedIntentEngine) RecognizeIntent(ctx context.Context, sessionID, message string) (*EnhancedIntentResult, error) {
	start := time.Now()

	eie.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"message":    message,
	}).Info("Enhanced intent recognition started")

	// 获取上下文
	conversationContext := eie.contextManager.GetOrCreateContext(sessionID, 1)

	// 预处理消息
	normalizedMessage := eie.normalizeMessage(message)

	// 简化处理 - 不调用实际的DeepSeek API
	eie.logger.Info("Enhanced intent classification - using simplified processing")

	// 直接返回默认分类结果
	result := eie.createFallbackResult(normalizedMessage)

	// 应用上下文增强
	if eie.config.ContextBoostEnabled {
		eie.applyContextBoost(result, conversationContext)
	}

	// 风险评估
	if eie.config.RiskAssessmentEnabled {
		eie.assessRisk(result, normalizedMessage)
	}

	// 生成执行计划
	if result.RiskLevel != "low" {
		result.ExecutionPlan = eie.generateExecutionPlan(result)
	}

	// 更新上下文
	eie.updateConversationContext(sessionID, result)

	result.Timestamp = time.Now()

	eie.logger.WithFields(logrus.Fields{
		"session_id":  sessionID,
		"intent_type": result.Type,
		"confidence":  result.Confidence,
		"risk_level":  result.RiskLevel,
		"duration_ms": time.Since(start).Milliseconds(),
	}).Info("Enhanced intent recognition completed")

	return result, nil
}

// normalizeMessage 消息标准化
func (eie *EnhancedIntentEngine) normalizeMessage(message string) string {
	// 去除多余空格
	message = regexp.MustCompile(`\s+`).ReplaceAllString(strings.TrimSpace(message), " ")

	// 统一中文标点
	message = strings.ReplaceAll(message, "，", ",")
	message = strings.ReplaceAll(message, "。", ".")
	message = strings.ReplaceAll(message, "？", "?")
	message = strings.ReplaceAll(message, "！", "!")

	return message
}

// extractSmartParameters 智能参数提取
func (eie *EnhancedIntentEngine) extractSmartParameters(message string) map[string]interface{} {
	params := make(map[string]interface{})

	// IP地址提取
	ipRegex := regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)
	if ips := ipRegex.FindAllString(message, -1); len(ips) > 0 {
		if len(ips) == 1 {
			params["ip_address"] = ips[0]
		} else {
			params["ip_addresses"] = ips
		}
	}

	// 端口号提取
	portRegex := regexp.MustCompile(`端口\s*[:：]?\s*(\d+)|port\s*[:：]?\s*(\d+)`)
	if matches := portRegex.FindStringSubmatch(message); len(matches) > 1 {
		for i := 1; i < len(matches); i++ {
			if matches[i] != "" {
				params["port"] = matches[i]
				break
			}
		}
	}

	// 服务名称提取
	serviceRegex := regexp.MustCompile(`(nginx|apache|mysql|redis|docker|kubernetes|k8s|tomcat|java|python|node)`)
	if services := serviceRegex.FindAllString(strings.ToLower(message), -1); len(services) > 0 {
		params["services"] = services
	}

	// 时间范围提取
	timeRegex := regexp.MustCompile(`(昨天|今天|上周|本周|上月|本月|最近\s*\d+\s*[天小时分钟]|last\s+\d+\s+(days?|hours?|minutes?))`)
	if timeRanges := timeRegex.FindAllString(message, -1); len(timeRanges) > 0 {
		params["time_ranges"] = timeRanges
	}

	// 文件路径提取
	pathRegex := regexp.MustCompile(`(/[^\s]+|[A-Z]:\\[^\s]+)`)
	if paths := pathRegex.FindAllString(message, -1); len(paths) > 0 {
		params["file_paths"] = paths
	}

	return params
}

// buildEnhancedSystemPrompt 构建增强的系统提示词
func (eie *EnhancedIntentEngine) buildEnhancedSystemPrompt(context *ConversationContext) string {
	basePrompt := CommercialIntentSystemPrompt

	// 根据上下文添加特定指导
	if context != nil && len(context.History) > 0 {
		basePrompt += "\n\n## 🔄 上下文信息：\n"
		basePrompt += "用户在此次对话中的历史操作：\n"

		for i, item := range context.History {
			if i >= 3 { // 只显示最近3次操作
				break
			}
			basePrompt += fmt.Sprintf("- %s: %s\n", item.Intent, item.Content)
		}

		basePrompt += "\n请结合上下文信息进行更精确的意图识别。"
	}

	return basePrompt
}

// buildEnhancedUserPrompt 构建增强的用户提示词
func (eie *EnhancedIntentEngine) buildEnhancedUserPrompt(
	message string,
	extractedParams map[string]interface{},
	context *ConversationContext,
) string {
	prompt := fmt.Sprintf("用户输入：%s\n", message)

	if len(extractedParams) > 0 {
		prompt += "\n预提取的参数：\n"
		for key, value := range extractedParams {
			prompt += fmt.Sprintf("- %s: %v\n", key, value)
		}
	}

	if context != nil && len(context.History) > 0 {
		prompt += fmt.Sprintf("\n当前对话意图：%s\n", context.History[0].Intent)
	}

	prompt += "\n请根据以上信息进行精确的意图识别和参数提取。"

	return prompt
}

// parseEnhancedResponse 解析增强响应
func (eie *EnhancedIntentEngine) parseEnhancedResponse(content string) (*EnhancedIntentResult, error) {
	// 清理响应内容
	content = strings.TrimSpace(content)

	// 尝试提取JSON部分
	jsonStart := strings.Index(content, "{")
	jsonEnd := strings.LastIndex(content, "}")

	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonContent := content[jsonStart : jsonEnd+1]

	var result EnhancedIntentResult
	if err := json.Unmarshal([]byte(jsonContent), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	// 验证必要字段
	if result.Type == "" {
		return nil, fmt.Errorf("missing intent type in response")
	}

	if result.Confidence < 0 || result.Confidence > 1 {
		result.Confidence = 0.5 // 默认置信度
	}

	if result.RiskLevel == "" {
		result.RiskLevel = "medium" // 默认风险等级
	}

	if result.Parameters == nil {
		result.Parameters = make(map[string]interface{})
	}

	return &result, nil
}

// createFallbackResult 创建降级结果
func (eie *EnhancedIntentEngine) createFallbackResult(message string) *EnhancedIntentResult {
	return &EnhancedIntentResult{
		Type:        "general_chat",
		Confidence:  0.3,
		RiskLevel:   "low",
		Parameters:  map[string]interface{}{"original_message": message},
		Context:     map[string]interface{}{"fallback": true},
		Suggestions: []string{"请提供更具体的操作指令", "您可以说'帮助'查看可用命令"},
		Timestamp:   time.Now(),
	}
}

// applyContextBoost 应用上下文增强
func (eie *EnhancedIntentEngine) applyContextBoost(result *EnhancedIntentResult, context *ConversationContext) {
	if context == nil || len(context.History) == 0 {
		return
	}

	// 检查是否与最近的意图相关
	lastIntent := context.History[0].Intent
	if lastIntent == result.Type {
		// 相同意图，提升置信度
		result.Confidence = minFloat64(result.Confidence+0.1, 1.0)
		result.Context["context_boost"] = "same_intent_continuation"
	}

	// 检查是否是相关的工作流
	relatedIntents := map[string][]string{
		"host_management":     {"infrastructure_monitoring", "troubleshooting"},
		"troubleshooting":     {"log_analysis", "network_diagnostics"},
		"service_management":  {"process_management", "performance_analysis"},
		"automation_workflow": {"deployment_management", "service_management"},
	}

	if related, exists := relatedIntents[lastIntent]; exists {
		for _, relatedIntent := range related {
			if relatedIntent == result.Type {
				result.Confidence = minFloat64(result.Confidence+0.05, 1.0)
				result.Context["context_boost"] = "related_intent_flow"
				break
			}
		}
	}
}

// assessRisk 风险评估
func (eie *EnhancedIntentEngine) assessRisk(result *EnhancedIntentResult, message string) {
	messageLower := strings.ToLower(message)

	// 检查高风险关键词
	for _, keyword := range HighRiskKeywords {
		if strings.Contains(messageLower, strings.ToLower(keyword)) {
			result.RiskLevel = "high"
			result.RequiresConfirm = true
			result.Context["risk_reason"] = fmt.Sprintf("Contains high-risk keyword: %s", keyword)
			return
		}
	}

	// 检查中风险关键词
	for _, keyword := range MediumRiskKeywords {
		if strings.Contains(messageLower, strings.ToLower(keyword)) {
			if result.RiskLevel != "high" {
				result.RiskLevel = "medium"
				result.Context["risk_reason"] = fmt.Sprintf("Contains medium-risk keyword: %s", keyword)
			}
		}
	}

	// 批量操作风险评估
	batchKeywords := []string{"批量", "所有", "全部", "batch", "all"}
	for _, keyword := range batchKeywords {
		if strings.Contains(messageLower, strings.ToLower(keyword)) {
			result.RiskLevel = "high"
			result.RequiresConfirm = true
			result.Context["risk_reason"] = "Batch operation detected"
			return
		}
	}

	// 生产环境风险评估
	prodKeywords := []string{"生产", "production", "prod"}
	for _, keyword := range prodKeywords {
		if strings.Contains(messageLower, strings.ToLower(keyword)) {
			if result.RiskLevel == "low" {
				result.RiskLevel = "medium"
			}
			result.Context["environment_risk"] = "production_environment"
		}
	}
}

// generateExecutionPlan 生成执行计划
func (eie *EnhancedIntentEngine) generateExecutionPlan(result *EnhancedIntentResult) *EnhancedExecutionPlan {
	plan := &EnhancedExecutionPlan{
		Steps:         []EnhancedExecutionStep{},
		EstimatedTime: 30,
		Prerequisites: []string{},
		RollbackPlan:  []string{},
	}

	switch result.Type {
	case "host_management":
		plan.Steps = append(plan.Steps, EnhancedExecutionStep{
			ID:          "validate_host_info",
			Description: "验证主机信息",
			Type:        "validation",
			Parameters:  map[string]interface{}{"validation_type": "host_connectivity"},
			RiskLevel:   "low",
			Timeout:     10,
		})
		plan.Prerequisites = []string{"确保网络连通性", "验证SSH访问权限"}

	case "service_management":
		plan.Steps = append(plan.Steps, EnhancedExecutionStep{
			ID:          "check_service_status",
			Description: "检查服务当前状态",
			Type:        "monitoring",
			Parameters:  map[string]interface{}{"check_type": "service_status"},
			RiskLevel:   "low",
			Timeout:     5,
		})
		plan.RollbackPlan = []string{"恢复服务到原始状态", "检查服务依赖"}

	case "automation_workflow":
		plan.EstimatedTime = 300 // 5分钟
		plan.Steps = append(plan.Steps, EnhancedExecutionStep{
			ID:          "prepare_batch_operation",
			Description: "准备批量操作",
			Type:        "preparation",
			Parameters:  map[string]interface{}{"batch_size": 10},
			RiskLevel:   "medium",
			Timeout:     60,
		})
		plan.Prerequisites = []string{"备份当前配置", "准备回滚方案", "通知相关人员"}
		plan.RollbackPlan = []string{"停止批量操作", "恢复备份配置", "验证系统状态"}
	}

	return plan
}

// updateConversationContext 更新对话上下文
func (eie *EnhancedIntentEngine) updateConversationContext(sessionID string, result *EnhancedIntentResult) {
	context := eie.contextManager.GetOrCreateContext(sessionID, 1)

	// 简化历史记录处理
	eie.logger.Debug("History update skipped in simplified mode")

	// 更新最后活动时间
	context.UpdatedAt = time.Now()

	// 保存上下文 (暂时跳过，因为ContextManager没有SaveContext方法)
	// eie.contextManager.SaveContext(sessionID, context)
}

// minFloat64 辅助函数
func minFloat64(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
