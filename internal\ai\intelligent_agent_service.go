package ai

import (
	"aiops-platform/internal/agent"
	"context"
	"fmt"
	"time"

	// "aiops-platform/internal/agent" // 暂时注释掉避免循环导入

	"github.com/sirupsen/logrus"
)

// IntelligentAgentService 智能Agent服务
type IntelligentAgentService struct {
	// 核心组件
	dispatcher           *DeepSeekAgentDispatcher
	coordinator          *SmartExecutionCoordinator
	capabilityDescriptor *AgentCapabilityDescriptor

	// 依赖服务
	deepseekService interface{} // 简化为interface{}
	agentRegistry   *agent.AgentRegistry
	executionEngine *agent.ExecutionEngine

	// 配置和日志
	logger *logrus.Logger
	config *IntelligentServiceConfig
}

// IntelligentServiceConfig 智能服务配置
type IntelligentServiceConfig struct {
	EnableAutoExecution   bool          `json:"enable_auto_execution"`
	DefaultTimeout        time.Duration `json:"default_timeout"`
	MaxConcurrentTasks    int           `json:"max_concurrent_tasks"`
	EnableFallback        bool          `json:"enable_fallback"`
	ConfidenceThreshold   float64       `json:"confidence_threshold"`
	EnableDetailedLogging bool          `json:"enable_detailed_logging"`
}

// ProcessIntelligentRequest 智能处理请求
type ProcessIntelligentRequest struct {
	UserMessage   string                 `json:"user_message"`
	SessionID     string                 `json:"session_id"`
	UserID        int64                  `json:"user_id"`
	Context       map[string]interface{} `json:"context"`
	AvailableData map[string]interface{} `json:"available_data"`
	Options       *ProcessOptions        `json:"options"`
}

// ProcessOptions 处理选项
type ProcessOptions struct {
	AutoExecute    *bool         `json:"auto_execute,omitempty"`
	Timeout        time.Duration `json:"timeout,omitempty"`
	Strategy       string        `json:"strategy,omitempty"`
	EnableRetry    *bool         `json:"enable_retry,omitempty"`
	MaxRetryCount  int           `json:"max_retry_count,omitempty"`
	RequireConfirm bool          `json:"require_confirm,omitempty"`
}

// ProcessIntelligentResponse 智能处理响应
type ProcessIntelligentResponse struct {
	// 调度结果
	DispatchResult *AgentDispatchResult `json:"dispatch_result"`

	// 执行结果
	ExecutionSession *SmartExecutionSession `json:"execution_session,omitempty"`

	// 响应信息
	Message        string        `json:"message"`
	Confidence     float64       `json:"confidence"`
	ProcessingTime time.Duration `json:"processing_time"`
	Timestamp      time.Time     `json:"timestamp"`

	// 状态信息
	Status         string   `json:"status"`
	RequireConfirm bool     `json:"require_confirm"`
	NextSteps      []string `json:"next_steps,omitempty"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// NewIntelligentAgentService 创建智能Agent服务
func NewIntelligentAgentService(
	deepseekService interface{}, // 简化为interface{}
	agentRegistry *agent.AgentRegistry,
	executionEngine *agent.ExecutionEngine,
	logger *logrus.Logger,
) *IntelligentAgentService {
	config := &IntelligentServiceConfig{
		EnableAutoExecution:   true,
		DefaultTimeout:        5 * time.Minute,
		MaxConcurrentTasks:    10,
		EnableFallback:        true,
		ConfidenceThreshold:   0.7,
		EnableDetailedLogging: true,
	}

	// 创建核心组件
	dispatcher := NewDeepSeekAgentDispatcher(deepseekService, agentRegistry, logger)
	coordinator := NewSmartExecutionCoordinator(agentRegistry, executionEngine, logger)
	capabilityDescriptor := NewAgentCapabilityDescriptor(logger)

	return &IntelligentAgentService{
		dispatcher:           dispatcher,
		coordinator:          coordinator,
		capabilityDescriptor: capabilityDescriptor,
		deepseekService:      deepseekService,
		agentRegistry:        agentRegistry,
		executionEngine:      executionEngine,
		logger:               logger,
		config:               config,
	}
}

// ProcessMessage 智能处理消息
func (ias *IntelligentAgentService) ProcessMessage(ctx context.Context, req *ProcessIntelligentRequest) (*ProcessIntelligentResponse, error) {
	start := time.Now()

	ias.logger.WithFields(logrus.Fields{
		"user_message": req.UserMessage,
		"session_id":   req.SessionID,
		"user_id":      req.UserID,
	}).Info("Processing intelligent message")

	// 1. 构建调度请求
	dispatchRequest := &AgentDispatchRequest{
		UserMessage:   req.UserMessage,
		SessionID:     req.SessionID,
		UserID:        req.UserID,
		Context:       req.Context,
		AvailableData: req.AvailableData,
	}

	// 2. 调用DeepSeek进行智能调度
	dispatchResult, err := ias.dispatcher.DispatchAgents(ctx, dispatchRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to dispatch agents: %w", err)
	}

	// 3. 构建基础响应
	response := &ProcessIntelligentResponse{
		DispatchResult: dispatchResult,
		Confidence:     dispatchResult.Confidence,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		Metadata:       make(map[string]interface{}),
	}

	// 4. 判断是否需要确认
	autoExecute := ias.shouldAutoExecute(req, dispatchResult)
	if !autoExecute {
		response.Status = "pending_confirmation"
		response.RequireConfirm = true
		response.Message = ias.buildConfirmationMessage(dispatchResult)
		response.NextSteps = ias.buildNextSteps(dispatchResult)
		return response, nil
	}

	// 5. 自动执行
	executionSession, err := ias.coordinator.ExecuteDispatchResult(
		ctx,
		req.SessionID,
		req.UserID,
		dispatchResult,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to execute dispatch result: %w", err)
	}

	response.ExecutionSession = executionSession
	response.Status = "executing"
	response.Message = ias.buildExecutionMessage(dispatchResult, executionSession)

	// 6. 等待执行完成（可选）
	if req.Options != nil && req.Options.Timeout > 0 {
		ias.waitForCompletion(ctx, executionSession, req.Options.Timeout)
		response.Status = string(executionSession.Status)
		response.Message = ias.buildCompletionMessage(executionSession)
	}

	ias.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"dispatch_result": len(dispatchResult.SelectedAgents),
		"auto_execute":    autoExecute,
		"status":          response.Status,
		"processing_time": response.ProcessingTime,
	}).Info("Intelligent message processing completed")

	return response, nil
}

// shouldAutoExecute 判断是否应该自动执行
func (ias *IntelligentAgentService) shouldAutoExecute(req *ProcessIntelligentRequest, result *AgentDispatchResult) bool {
	// 检查全局配置
	if !ias.config.EnableAutoExecution {
		return false
	}

	// 检查请求选项
	if req.Options != nil {
		if req.Options.AutoExecute != nil {
			return *req.Options.AutoExecute
		}
		if req.Options.RequireConfirm {
			return false
		}
	}

	// 检查置信度
	if result.Confidence < ias.config.ConfidenceThreshold {
		return false
	}

	// 检查Agent数量（复杂任务需要确认）
	if len(result.SelectedAgents) > 3 {
		return false
	}

	// 检查是否包含高风险操作
	for _, selectedAgent := range result.SelectedAgents {
		if ias.isHighRiskOperation(selectedAgent) {
			return false
		}
	}

	return true
}

// isHighRiskOperation 判断是否为高风险操作
func (ias *IntelligentAgentService) isHighRiskOperation(selectedAgent *SelectedAgent) bool {
	highRiskCapabilities := []string{
		"delete_host",
		"execute_command",
		"modify_config",
		"restart_service",
		"backup_restore",
	}

	for _, riskyCap := range highRiskCapabilities {
		if selectedAgent.Capability == riskyCap {
			return true
		}
	}

	return false
}

// buildConfirmationMessage 构建确认消息
func (ias *IntelligentAgentService) buildConfirmationMessage(result *AgentDispatchResult) string {
	message := fmt.Sprintf("🤖 我理解您的需求，计划执行以下操作：\n\n")

	for i, selectedAgent := range result.SelectedAgents {
		message += fmt.Sprintf("%d. **%s** - %s\n", i+1, selectedAgent.AgentName, selectedAgent.Description)
		if len(selectedAgent.Parameters) > 0 {
			message += "   参数: "
			for key, value := range selectedAgent.Parameters {
				message += fmt.Sprintf("%s=%v ", key, value)
			}
			message += "\n"
		}
	}

	message += fmt.Sprintf("\n**执行策略**: %s\n", result.ExecutionPlan.Strategy)
	message += fmt.Sprintf("**预估时间**: %v\n", result.EstimatedTime)
	message += fmt.Sprintf("**置信度**: %.1f%%\n\n", result.Confidence*100)
	message += "是否确认执行？请回复 '确认' 或 '取消'"

	return message
}

// buildExecutionMessage 构建执行消息
func (ias *IntelligentAgentService) buildExecutionMessage(result *AgentDispatchResult, session *SmartExecutionSession) string {
	message := fmt.Sprintf("🚀 正在执行您的请求...\n\n")
	message += fmt.Sprintf("**会话ID**: %s\n", session.SessionID)
	message += fmt.Sprintf("**执行策略**: %s\n", result.ExecutionPlan.Strategy)
	message += fmt.Sprintf("**总步骤数**: %d\n", session.TotalSteps)
	message += fmt.Sprintf("**开始时间**: %s\n", session.StartTime.Format("15:04:05"))

	if len(result.SelectedAgents) > 0 {
		message += "\n**执行计划**:\n"
		for i, agent := range result.SelectedAgents {
			message += fmt.Sprintf("%d. %s - %s\n", i+1, agent.AgentName, agent.Description)
		}
	}

	return message
}

// buildCompletionMessage 构建完成消息
func (ias *IntelligentAgentService) buildCompletionMessage(session *SmartExecutionSession) string {
	switch session.Status {
	case StatusCompleted:
		message := "✅ 任务执行完成！\n\n"
		message += fmt.Sprintf("**执行时间**: %v\n", session.EndTime.Sub(session.StartTime))
		message += fmt.Sprintf("**成功步骤**: %d/%d\n", ias.countSuccessfulSteps(session), session.TotalSteps)

		if len(session.Results) > 0 {
			message += "\n**执行结果**:\n"
			for i, result := range session.Results {
				status := "✅"
				if !result.Success {
					status = "❌"
				}
				message += fmt.Sprintf("%d. %s %s (%v)\n", i+1, status, result.Capability, result.Duration)
			}
		}
		return message

	case StatusFailed:
		message := "❌ 任务执行失败\n\n"
		message += fmt.Sprintf("**错误信息**: %s\n", session.ErrorMessage)
		message += fmt.Sprintf("**执行时间**: %v\n", session.EndTime.Sub(session.StartTime))
		message += fmt.Sprintf("**完成步骤**: %d/%d\n", len(session.Results), session.TotalSteps)
		return message

	case StatusCancelled:
		return "⏹️ 任务已取消"

	case StatusTimeout:
		return "⏰ 任务执行超时"

	default:
		return fmt.Sprintf("📊 任务状态: %s", session.Status)
	}
}

// buildNextSteps 构建下一步建议
func (ias *IntelligentAgentService) buildNextSteps(result *AgentDispatchResult) []string {
	steps := []string{
		"回复 '确认' 开始执行",
		"回复 '取消' 取消操作",
		"回复 '修改参数' 调整执行参数",
	}

	if len(result.RequiredData) > 0 {
		steps = append(steps, "提供缺失的必要信息: "+fmt.Sprintf("%v", result.RequiredData))
	}

	return steps
}

// countSuccessfulSteps 统计成功步骤数
func (ias *IntelligentAgentService) countSuccessfulSteps(session *SmartExecutionSession) int {
	count := 0
	for _, result := range session.Results {
		if result.Success {
			count++
		}
	}
	return count
}

// waitForCompletion 等待执行完成
func (ias *IntelligentAgentService) waitForCompletion(ctx context.Context, session *SmartExecutionSession, timeout time.Duration) {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			return
		case <-ticker.C:
			if session.Status == StatusCompleted || session.Status == StatusFailed || session.Status == StatusCancelled {
				return
			}
		}
	}
}

// GetExecutionSession 获取执行会话
func (ias *IntelligentAgentService) GetExecutionSession(sessionID string) (*SmartExecutionSession, bool) {
	return ias.coordinator.GetSession(sessionID)
}

// CancelExecution 取消执行
func (ias *IntelligentAgentService) CancelExecution(sessionID string) error {
	return ias.coordinator.CancelSession(sessionID)
}

// GetAgentCapabilities 获取Agent能力描述
func (ias *IntelligentAgentService) GetAgentCapabilities() (string, error) {
	return ias.dispatcher.buildAgentCapabilitiesDescription()
}

// GetServiceStatus 获取服务状态
func (ias *IntelligentAgentService) GetServiceStatus() map[string]interface{} {
	activeSessions := ias.coordinator.GetActiveSessions()

	return map[string]interface{}{
		"service_name":         "IntelligentAgentService",
		"status":               "running",
		"active_sessions":      len(activeSessions),
		"max_concurrent":       ias.config.MaxConcurrentTasks,
		"auto_execution":       ias.config.EnableAutoExecution,
		"confidence_threshold": ias.config.ConfidenceThreshold,
		"registered_agents":    0, // 简化处理
		"uptime":               time.Now().Format("2006-01-02 15:04:05"),
	}
}
