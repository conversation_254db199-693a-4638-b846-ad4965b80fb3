package ai

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"github.com/sirupsen/logrus"
)

// RobustAIService 健壮的AI服务
type RobustAIService struct {
	// 主要服务
	commercialService interface{} // 简化为interface{}

	// 降级服务
	localProcessor *LocalIntentProcessor
	patternMatcher *PatternMatcher

	// 配置
	config *RobustAIConfig
	logger *logrus.Logger

	// 状态管理
	deepseekAvailable bool
	lastHealthCheck   time.Time
}

// RobustAIConfig 健壮AI配置
type RobustAIConfig struct {
	EnableLocalFallback   bool          `json:"enable_local_fallback"`
	EnablePatternMatching bool          `json:"enable_pattern_matching"`
	HealthCheckInterval   time.Duration `json:"health_check_interval"`
	MaxRetryAttempts      int           `json:"max_retry_attempts"`
	RetryDelay            time.Duration `json:"retry_delay"`
	DeepSeekTimeout       time.Duration `json:"deepseek_timeout"`
}

// LocalIntentProcessor 本地意图处理器
type LocalIntentProcessor struct {
	patterns map[string]*LocalPattern
	logger   *logrus.Logger
}

// LocalPattern 本地模式
type LocalPattern struct {
	Intent     string            `json:"intent"`
	Patterns   []string          `json:"patterns"`
	Parameters map[string]string `json:"parameters"`
	Confidence float64           `json:"confidence"`
	Action     string            `json:"action"`
}

// PatternMatcher 模式匹配器
type PatternMatcher struct {
	hostPatterns     []*regexp.Regexp
	passwordPatterns []*regexp.Regexp
	commandPatterns  []*regexp.Regexp
	logger           *logrus.Logger
}

// NewRobustAIService 创建健壮AI服务
func NewRobustAIService(
	commercialService interface{}, // 简化为interface{}
	logger *logrus.Logger,
) *RobustAIService {
	config := &RobustAIConfig{
		EnableLocalFallback:   true,
		EnablePatternMatching: true,
		HealthCheckInterval:   1 * time.Minute,
		MaxRetryAttempts:      3,
		RetryDelay:            2 * time.Second,
		DeepSeekTimeout:       15 * time.Second, // 减少超时时间
	}

	return &RobustAIService{
		commercialService: commercialService,
		localProcessor:    NewLocalIntentProcessor(logger),
		patternMatcher:    NewPatternMatcher(logger),
		config:            config,
		logger:            logger,
		deepseekAvailable: true,
		lastHealthCheck:   time.Now(),
	}
}

// NewLocalIntentProcessor 创建本地意图处理器
func NewLocalIntentProcessor(logger *logrus.Logger) *LocalIntentProcessor {
	processor := &LocalIntentProcessor{
		patterns: make(map[string]*LocalPattern),
		logger:   logger,
	}

	// 初始化本地模式
	processor.initializeLocalPatterns()
	return processor
}

// initializeLocalPatterns 初始化本地模式
func (lip *LocalIntentProcessor) initializeLocalPatterns() {
	// 主机密码修改模式
	lip.patterns["modify_host_password"] = &LocalPattern{
		Intent: "host_management",
		Patterns: []string{
			`修改.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?密码.*?(\S+)`,
			`更改.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?密码.*?(\S+)`,
			`change.*?password.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?(\S+)`,
		},
		Parameters: map[string]string{
			"operation":      "update_password",
			"ip_group":       "1",
			"password_group": "2",
		},
		Confidence: 0.95,
		Action:     "update_host_password",
	}

	// 主机添加模式
	lip.patterns["add_host"] = &LocalPattern{
		Intent: "host_management",
		Patterns: []string{
			`添加.*?主机.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?用户名.*?(\w+).*?密码.*?(\S+)`,
			`新增.*?服务器.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?(\w+).*?(\S+)`,
		},
		Parameters: map[string]string{
			"operation":      "add_host",
			"ip_group":       "1",
			"username_group": "2",
			"password_group": "3",
		},
		Confidence: 0.9,
		Action:     "add_host",
	}

	// 主机列表查询模式
	lip.patterns["list_hosts"] = &LocalPattern{
		Intent: "host_management",
		Patterns: []string{
			`查看.*?主机.*?列表`,
			`显示.*?所有.*?主机`,
			`list.*?hosts?`,
			`show.*?servers?`,
		},
		Parameters: map[string]string{
			"operation": "list_hosts",
		},
		Confidence: 0.85,
		Action:     "list_hosts",
	}

	// 故障诊断模式
	lip.patterns["troubleshooting"] = &LocalPattern{
		Intent: "troubleshooting",
		Patterns: []string{
			`检查.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?连接`,
			`诊断.*?(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?问题`,
			`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}).*?连不上`,
		},
		Parameters: map[string]string{
			"operation": "connection_diagnosis",
			"ip_group":  "1",
		},
		Confidence: 0.88,
		Action:     "diagnose_connection",
	}
}

// NewPatternMatcher 创建模式匹配器
func NewPatternMatcher(logger *logrus.Logger) *PatternMatcher {
	return &PatternMatcher{
		hostPatterns: []*regexp.Regexp{
			regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`), // IP地址
			regexp.MustCompile(`\b\w+[-\w]*\.\w+\b`),          // 域名
		},
		passwordPatterns: []*regexp.Regexp{
			regexp.MustCompile(`密码.*?为.*?(\S+)`),
			regexp.MustCompile(`password.*?(\S+)`),
		},
		commandPatterns: []*regexp.Regexp{
			regexp.MustCompile(`执行.*?命令.*?(\S+)`),
			regexp.MustCompile(`运行.*?(\S+)`),
		},
		logger: logger,
	}
}

// ProcessMessage 处理消息（健壮版）
func (ras *RobustAIService) ProcessMessage(ctx context.Context, req *CommercialAIRequest) (*CommercialAIResponse, error) {
	start := time.Now()

	ras.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("RobustAIService: Starting message processing")

	// 检查DeepSeek服务健康状态
	if time.Since(ras.lastHealthCheck) > ras.config.HealthCheckInterval {
		ras.checkDeepSeekHealth(ctx)
	}

	// 尝试使用商用级服务
	if ras.deepseekAvailable {
		response, err := ras.tryCommercialService(ctx, req)
		if err == nil {
			ras.logger.WithField("processing_time", time.Since(start)).Info("RobustAIService: Commercial service succeeded")
			return response, nil
		}

		ras.logger.WithError(err).Warn("RobustAIService: Commercial service failed, trying fallback")
		ras.deepseekAvailable = false
	}

	// 降级到本地处理
	return ras.processWithLocalFallback(ctx, req, start)
}

// tryCommercialService 尝试使用商用级服务
func (ras *RobustAIService) tryCommercialService(ctx context.Context, req *CommercialAIRequest) (*CommercialAIResponse, error) {
	// 简化处理 - 不调用实际的商业服务
	ras.logger.Info("Commercial service call - using simplified processing")
	return nil, fmt.Errorf("commercial service not available in simplified mode")
}

// processWithLocalFallback 使用本地降级处理
func (ras *RobustAIService) processWithLocalFallback(ctx context.Context, req *CommercialAIRequest, start time.Time) (*CommercialAIResponse, error) {
	ras.logger.Info("RobustAIService: Using local fallback processing")

	// 使用本地意图处理器
	localResult, err := ras.localProcessor.ProcessMessage(req.Message)
	if err != nil {
		return ras.createErrorResponse(req, "本地处理失败", start), nil
	}

	// 构建响应
	response := &CommercialAIResponse{
		Success:   true,
		Message:   ras.generateLocalResponse(localResult),
		Data:      localResult.Data,
		Actions:   ras.convertToActions(localResult),
		NextSteps: ras.generateNextSteps(localResult),
		IntentInfo: &IntentInfo{
			Type:       localResult.Intent,
			Confidence: localResult.Confidence,
			RiskLevel:  ras.assessLocalRisk(localResult),
			Category:   "本地处理",
		},
		SecurityInfo: &SecurityInfo{
			Validated:       true,
			RiskAssessment:  "low",
			PermissionCheck: "passed",
			RequiresConfirm: ras.requiresConfirmation(localResult),
		},
		ExecutionTime: time.Since(start),
		RequestID:     fmt.Sprintf("local_%d", time.Now().UnixNano()),
		Timestamp:     time.Now(),
	}

	ras.logger.WithFields(logrus.Fields{
		"intent":     localResult.Intent,
		"confidence": localResult.Confidence,
		"action":     localResult.Action,
	}).Info("RobustAIService: Local fallback processing completed")

	return response, nil
}

// LocalProcessResult 本地处理结果
type LocalProcessResult struct {
	Intent     string                 `json:"intent"`
	Action     string                 `json:"action"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Data       map[string]interface{} `json:"data"`
}

// ProcessMessage 本地处理消息
func (lip *LocalIntentProcessor) ProcessMessage(message string) (*LocalProcessResult, error) {
	// 遍历所有模式进行匹配
	for patternName, pattern := range lip.patterns {
		for _, regexStr := range pattern.Patterns {
			regex := regexp.MustCompile(regexStr)
			if matches := regex.FindStringSubmatch(message); matches != nil {
				lip.logger.WithFields(logrus.Fields{
					"pattern": patternName,
					"matches": matches,
				}).Info("LocalIntentProcessor: Pattern matched")

				// 提取参数
				parameters := make(map[string]interface{})
				data := make(map[string]interface{})

				for paramName, groupStr := range pattern.Parameters {
					if paramName == "ip_group" || paramName == "username_group" || paramName == "password_group" {
						if groupStr == "1" && len(matches) > 1 {
							if paramName == "ip_group" {
								parameters["ip_address"] = matches[1]
								data["target_host"] = matches[1]
							} else if paramName == "username_group" {
								parameters["username"] = matches[1]
								data["username"] = matches[1]
							} else if paramName == "password_group" {
								parameters["password"] = matches[1]
								data["password"] = "***" // 隐藏密码
							}
						} else if groupStr == "2" && len(matches) > 2 {
							if paramName == "password_group" {
								parameters["password"] = matches[2]
								data["password"] = "***"
							} else if paramName == "username_group" {
								parameters["username"] = matches[2]
								data["username"] = matches[2]
							}
						} else if groupStr == "3" && len(matches) > 3 {
							if paramName == "password_group" {
								parameters["password"] = matches[3]
								data["password"] = "***"
							}
						}
					} else {
						parameters[paramName] = pattern.Parameters[paramName]
						data[paramName] = pattern.Parameters[paramName]
					}
				}

				return &LocalProcessResult{
					Intent:     pattern.Intent,
					Action:     pattern.Action,
					Confidence: pattern.Confidence,
					Parameters: parameters,
					Data:       data,
				}, nil
			}
		}
	}

	// 如果没有匹配到模式，返回通用对话
	return &LocalProcessResult{
		Intent:     "general_chat",
		Action:     "general_response",
		Confidence: 0.3,
		Parameters: map[string]interface{}{"original_message": message},
		Data:       map[string]interface{}{"fallback": true},
	}, nil
}

// generateLocalResponse 生成本地响应
func (ras *RobustAIService) generateLocalResponse(result *LocalProcessResult) string {
	switch result.Action {
	case "update_host_password":
		if ip, exists := result.Parameters["ip_address"]; exists {
			return fmt.Sprintf("✅ 已识别到修改主机 %s 的密码请求。正在执行密码更新操作...", ip)
		}
		return "✅ 已识别到密码修改请求，正在处理..."

	case "add_host":
		if ip, exists := result.Parameters["ip_address"]; exists {
			return fmt.Sprintf("✅ 已识别到添加主机 %s 的请求。正在验证连接并添加到系统...", ip)
		}
		return "✅ 已识别到添加主机请求，正在处理..."

	case "list_hosts":
		return "📋 正在获取主机列表..."

	case "diagnose_connection":
		if ip, exists := result.Parameters["ip_address"]; exists {
			return fmt.Sprintf("🔍 开始诊断主机 %s 的连接问题...", ip)
		}
		return "🔍 开始进行连接诊断..."

	default:
		return "🤖 我理解了您的请求，正在使用本地处理模式为您服务。"
	}
}

// convertToActions 转换为操作项
func (ras *RobustAIService) convertToActions(result *LocalProcessResult) []ActionItem {
	actions := []ActionItem{
		{
			ID:          fmt.Sprintf("local_action_%d", time.Now().UnixNano()),
			Type:        result.Action,
			Description: ras.getActionDescription(result.Action),
			Status:      "pending",
			Parameters:  result.Parameters,
		},
	}
	return actions
}

// getActionDescription 获取操作描述
func (ras *RobustAIService) getActionDescription(action string) string {
	descriptions := map[string]string{
		"update_host_password": "更新主机密码",
		"add_host":             "添加新主机",
		"list_hosts":           "查看主机列表",
		"diagnose_connection":  "诊断连接问题",
		"general_response":     "通用响应处理",
	}

	if desc, exists := descriptions[action]; exists {
		return desc
	}
	return "执行操作"
}

// generateNextSteps 生成下一步建议
func (ras *RobustAIService) generateNextSteps(result *LocalProcessResult) []string {
	switch result.Action {
	case "update_host_password":
		return []string{
			"验证新密码强度",
			"备份当前配置",
			"执行密码更新",
			"测试SSH连接",
		}
	case "add_host":
		return []string{
			"验证主机连通性",
			"测试SSH连接",
			"添加到主机列表",
			"配置监控",
		}
	case "list_hosts":
		return []string{
			"显示主机状态",
			"检查连接状态",
			"更新监控数据",
		}
	default:
		return []string{
			"继续对话",
			"提供更多信息",
		}
	}
}

// assessLocalRisk 评估本地风险
func (ras *RobustAIService) assessLocalRisk(result *LocalProcessResult) string {
	switch result.Action {
	case "update_host_password":
		return "medium"
	case "add_host":
		return "low"
	case "list_hosts":
		return "low"
	case "diagnose_connection":
		return "low"
	default:
		return "low"
	}
}

// requiresConfirmation 是否需要确认
func (ras *RobustAIService) requiresConfirmation(result *LocalProcessResult) bool {
	return result.Action == "update_host_password"
}

// createErrorResponse 创建错误响应
func (ras *RobustAIService) createErrorResponse(req *CommercialAIRequest, errorMsg string, start time.Time) *CommercialAIResponse {
	return &CommercialAIResponse{
		Success:       false,
		Message:       fmt.Sprintf("❌ %s", errorMsg),
		Data:          make(map[string]interface{}),
		ExecutionTime: time.Since(start),
		RequestID:     fmt.Sprintf("error_%d", time.Now().UnixNano()),
		Timestamp:     time.Now(),
		Warnings:      []string{"系统正在使用降级模式"},
		NextSteps:     []string{"请稍后重试", "或联系技术支持"},
	}
}

// checkDeepSeekHealth 检查DeepSeek健康状态
func (ras *RobustAIService) checkDeepSeekHealth(ctx context.Context) {
	ras.lastHealthCheck = time.Now()

	// 简化健康检查
	ras.deepseekAvailable = false // 在简化模式下总是不可用

	ras.logger.WithFields(logrus.Fields{
		"available": ras.deepseekAvailable,
	}).Info("RobustAIService: DeepSeek health check completed")
}
