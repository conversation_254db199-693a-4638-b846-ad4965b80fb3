package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// UnifiedAIService 统一AI服务 - 整合所有AI功能的单一入口
type UnifiedAIService struct {
	// 核心组件
	intentEngine    *EnhancedIntentEngine
	executionEngine *EnhancedExecutionEngine
	securityManager *EnhancedSecurityManager
	contextManager  *ContextManager

	// 处理器注册表
	handlers map[string]IntentHandler

	// 配置和状态
	config  *UnifiedAIConfig
	logger  *logrus.Logger
	metrics *UnifiedAIMetrics

	// 并发控制
	requestLimiter chan struct{}
	mutex          sync.RWMutex
}

// UnifiedAIConfig 统一AI配置
type UnifiedAIConfig struct {
	// 性能配置
	MaxConcurrentRequests int           `json:"max_concurrent_requests"`
	RequestTimeout        time.Duration `json:"request_timeout"`
	CacheEnabled          bool          `json:"cache_enabled"`
	CacheTTL              time.Duration `json:"cache_ttl"`

	// AI配置
	DeepSeekTimeout     time.Duration `json:"deepseek_timeout"`
	FallbackEnabled     bool          `json:"fallback_enabled"`
	ConfidenceThreshold float64       `json:"confidence_threshold"`

	// 安全配置
	SecurityEnabled       bool `json:"security_enabled"`
	RiskAssessmentEnabled bool `json:"risk_assessment_enabled"`
	AuditEnabled          bool `json:"audit_enabled"`

	// 监控配置
	MetricsEnabled      bool          `json:"metrics_enabled"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
}

// UnifiedAIMetrics 统一AI指标
type UnifiedAIMetrics struct {
	// 请求统计
	TotalRequests      int64 `json:"total_requests"`
	SuccessfulRequests int64 `json:"successful_requests"`
	FailedRequests     int64 `json:"failed_requests"`
	CachedRequests     int64 `json:"cached_requests"`

	// 性能指标
	AverageResponseTime float64 `json:"average_response_time_ms"`
	P95ResponseTime     float64 `json:"p95_response_time_ms"`
	P99ResponseTime     float64 `json:"p99_response_time_ms"`

	// AI指标
	IntentAccuracy    float64 `json:"intent_accuracy"`
	FallbackRate      float64 `json:"fallback_rate"`
	SecurityBlockRate float64 `json:"security_block_rate"`

	// 分类统计
	RequestsByIntent  map[string]int64 `json:"requests_by_intent"`
	RequestsByHandler map[string]int64 `json:"requests_by_handler"`
	ErrorsByType      map[string]int64 `json:"errors_by_type"`

	// 时间戳
	LastUpdated time.Time `json:"last_updated"`

	// 并发保护
	mutex sync.RWMutex `json:"-"`
}

// UnifiedAIRequest 统一AI请求
type UnifiedAIRequest struct {
	// 基本信息
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`

	// 上下文信息
	Context   map[string]interface{} `json:"context"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`

	// 配置选项
	Options *RequestOptions `json:"options"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// RequestOptions 请求选项
type RequestOptions struct {
	EnableCache         bool          `json:"enable_cache"`
	EnableSecurity      bool          `json:"enable_security"`
	EnableFallback      bool          `json:"enable_fallback"`
	Timeout             time.Duration `json:"timeout"`
	ConfidenceThreshold float64       `json:"confidence_threshold"`
}

// UnifiedAIResponse 统一AI响应
type UnifiedAIResponse struct {
	// 基本信息
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`

	// 数据和操作
	Data      map[string]interface{} `json:"data"`
	Actions   []ActionItem           `json:"actions"`
	NextSteps []string               `json:"next_steps"`

	// AI信息
	IntentInfo *IntentInfo `json:"intent_info"`
	Confidence float64     `json:"confidence"`

	// 安全信息
	SecurityInfo *SecurityInfo `json:"security_info"`

	// 系统信息
	ExecutionTime time.Duration `json:"execution_time"`
	CacheHit      bool          `json:"cache_hit"`
	FallbackUsed  bool          `json:"fallback_used"`

	// 建议和警告
	Warnings        []string `json:"warnings"`
	Recommendations []string `json:"recommendations"`

	// 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// NewUnifiedAIService 创建统一AI服务
func NewUnifiedAIService(
	deepseekService interface{}, // 简化为interface{}
	hostService interface{}, // 简化为interface{}
	logger *logrus.Logger,
) *UnifiedAIService {
	config := &UnifiedAIConfig{
		MaxConcurrentRequests: 100,
		RequestTimeout:        30 * time.Second,
		CacheEnabled:          true,
		CacheTTL:              5 * time.Minute,
		DeepSeekTimeout:       15 * time.Second,
		FallbackEnabled:       true,
		ConfidenceThreshold:   0.8,
		SecurityEnabled:       true,
		RiskAssessmentEnabled: true,
		AuditEnabled:          true,
		MetricsEnabled:        true,
		HealthCheckInterval:   1 * time.Minute,
	}

	metrics := &UnifiedAIMetrics{
		RequestsByIntent:  make(map[string]int64),
		RequestsByHandler: make(map[string]int64),
		ErrorsByType:      make(map[string]int64),
		LastUpdated:       time.Now(),
	}

	// 创建核心组件
	contextManager := NewContextManager(logger, nil) // 使用默认配置
	intentEngine := NewEnhancedIntentEngine(deepseekService, contextManager, logger)
	securityManager := NewEnhancedSecurityManager(logger)

	// 创建意图路由器
	intentRouter := NewIntentRouter(logger)
	intentRouter.InitializeDefaultHandlers(hostService)

	// 创建执行引擎
	executionEngine := NewEnhancedExecutionEngine(intentRouter, intentEngine, logger)

	service := &UnifiedAIService{
		intentEngine:    intentEngine,
		executionEngine: executionEngine,
		securityManager: securityManager,
		contextManager:  contextManager,
		handlers:        make(map[string]IntentHandler),
		config:          config,
		logger:          logger,
		metrics:         metrics,
		requestLimiter:  make(chan struct{}, config.MaxConcurrentRequests),
	}

	// 注册默认处理器
	service.registerDefaultHandlers(hostService)

	logger.Info("UnifiedAIService initialized successfully")
	return service
}

// ProcessMessage 处理消息（统一入口）
func (uas *UnifiedAIService) ProcessMessage(ctx context.Context, req *UnifiedAIRequest) (*UnifiedAIResponse, error) {
	start := time.Now()

	// 生成请求ID
	if req.RequestID == "" {
		req.RequestID = fmt.Sprintf("unified_%d_%s", time.Now().UnixNano(), req.SessionID)
	}

	uas.logger.WithFields(logrus.Fields{
		"request_id": req.RequestID,
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("UnifiedAIService: Processing message")

	// 并发控制
	select {
	case uas.requestLimiter <- struct{}{}:
		defer func() { <-uas.requestLimiter }()
	case <-ctx.Done():
		return uas.createErrorResponse(req, "Request cancelled due to context timeout", start), nil
	default:
		return uas.createErrorResponse(req, "Too many concurrent requests", start), nil
	}

	// 更新指标
	uas.updateMetrics("total", 0, false, true)

	// 设置超时
	timeout := uas.config.RequestTimeout
	if req.Options != nil && req.Options.Timeout > 0 {
		timeout = req.Options.Timeout
	}

	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 第一步：意图识别
	intentResult, err := uas.intentEngine.RecognizeIntent(timeoutCtx, req.SessionID, req.Message)
	if err != nil {
		uas.updateMetrics("intent_failed", time.Since(start), false, false)
		return uas.createErrorResponse(req, fmt.Sprintf("意图识别失败: %v", err), start), nil
	}

	// 第二步：安全验证（如果启用）
	if uas.config.SecurityEnabled && (req.Options == nil || req.Options.EnableSecurity) {
		securityReq := &SecurityValidationRequest{
			UserID:     req.UserID,
			SessionID:  req.SessionID,
			Intent:     intentResult.Type,
			Parameters: intentResult.Parameters,
			RiskLevel:  intentResult.RiskLevel,
			IPAddress:  req.IPAddress,
			UserAgent:  req.UserAgent,
			Context:    req.Context,
		}

		securityResult, err := uas.securityManager.ValidateOperation(timeoutCtx, securityReq)
		if err != nil {
			uas.updateMetrics("security_error", time.Since(start), false, false)
			return uas.createErrorResponse(req, fmt.Sprintf("安全验证失败: %v", err), start), nil
		}

		if !securityResult.Allowed {
			uas.updateMetrics("security_blocked", time.Since(start), false, false)
			return &UnifiedAIResponse{
				Success:   false,
				Message:   "操作被安全策略阻止",
				RequestID: req.RequestID,
				SecurityInfo: &SecurityInfo{
					Validated:       false,
					RiskAssessment:  "blocked",
					PermissionCheck: "denied",
					RequiresConfirm: true,
				},
				ExecutionTime: time.Since(start),
				Timestamp:     time.Now(),
				Warnings:      []string{"操作被安全策略阻止"},
			}, nil
		}
	}

	// 第三步：执行处理
	execReq := &ExecutionRequest{
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		Message:     req.Message,
		Context:     req.Context,
		RequestedAt: time.Now(),
	}

	execResult, err := uas.executionEngine.ProcessMessage(timeoutCtx, execReq)
	if err != nil {
		uas.updateMetrics(intentResult.Type, time.Since(start), false, false)
		return uas.createErrorResponse(req, fmt.Sprintf("执行失败: %v", err), start), nil
	}

	// 第四步：构建响应
	response := &UnifiedAIResponse{
		Success:   execResult.Success,
		Message:   execResult.HandleResult.Message,
		RequestID: req.RequestID,
		Data:      execResult.HandleResult.Data,
		Actions:   execResult.HandleResult.Actions,
		NextSteps: execResult.HandleResult.NextSteps,
		IntentInfo: &IntentInfo{
			Type:       intentResult.Type,
			Confidence: intentResult.Confidence,
			RiskLevel:  intentResult.RiskLevel,
			Category:   uas.getIntentCategory(intentResult.Type),
		},
		Confidence:      intentResult.Confidence,
		ExecutionTime:   time.Since(start),
		Warnings:        execResult.Warnings,
		Recommendations: execResult.Recommendations,
		Timestamp:       time.Now(),
	}

	// 更新指标
	uas.updateMetrics(intentResult.Type, time.Since(start), execResult.Success, false)

	uas.logger.WithFields(logrus.Fields{
		"request_id":     req.RequestID,
		"success":        response.Success,
		"intent_type":    intentResult.Type,
		"execution_time": response.ExecutionTime.Milliseconds(),
	}).Info("UnifiedAIService: Message processing completed")

	return response, nil
}

// registerDefaultHandlers 注册默认处理器
func (uas *UnifiedAIService) registerDefaultHandlers(hostService interface{}) {
	// 主机管理处理器
	if hostSvc, ok := hostService.(interface{}); ok {
		hostHandler := NewHostManagementHandler(hostSvc, uas.logger)
		for _, intent := range hostHandler.GetSupportedIntents() {
			uas.handlers[intent] = hostHandler
		}
	}

	// 故障排查处理器
	troubleshootingHandler := NewTroubleshootingHandler(uas.logger)
	for _, intent := range troubleshootingHandler.GetSupportedIntents() {
		uas.handlers[intent] = troubleshootingHandler
	}

	uas.logger.WithField("handlers_count", len(uas.handlers)).Info("Default handlers registered")
}

// createErrorResponse 创建错误响应
func (uas *UnifiedAIService) createErrorResponse(req *UnifiedAIRequest, errorMsg string, start time.Time) *UnifiedAIResponse {
	return &UnifiedAIResponse{
		Success:       false,
		Message:       fmt.Sprintf("❌ %s", errorMsg),
		RequestID:     req.RequestID,
		Data:          make(map[string]interface{}),
		ExecutionTime: time.Since(start),
		Timestamp:     time.Now(),
		Warnings:      []string{"系统处理过程中发生错误"},
		NextSteps:     []string{"请检查输入并重试", "如问题持续，请联系技术支持"},
	}
}

// getIntentCategory 获取意图分类
func (uas *UnifiedAIService) getIntentCategory(intentType string) string {
	categories := map[string]string{
		"host_management":           "基础设施管理",
		"infrastructure_monitoring": "基础设施管理",
		"troubleshooting":           "故障诊断",
		"log_analysis":              "故障诊断",
		"network_diagnostics":       "故障诊断",
		"service_management":        "系统运维",
		"process_management":        "系统运维",
		"automation_workflow":       "自动化编排",
		"deployment_management":     "自动化编排",
		"security_audit":            "安全合规",
		"access_control":            "安全合规",
		"performance_analysis":      "性能分析",
		"compliance_reporting":      "报表分析",
	}

	if category, exists := categories[intentType]; exists {
		return category
	}
	return "通用对话"
}

// updateMetrics 更新指标
func (uas *UnifiedAIService) updateMetrics(metricType string, duration time.Duration, success bool, isTotal bool) {
	if !uas.config.MetricsEnabled {
		return
	}

	uas.metrics.mutex.Lock()
	defer uas.metrics.mutex.Unlock()

	if isTotal {
		uas.metrics.TotalRequests++
	} else {
		if success {
			uas.metrics.SuccessfulRequests++
		} else {
			uas.metrics.FailedRequests++
		}

		// 更新响应时间
		totalRequests := uas.metrics.SuccessfulRequests + uas.metrics.FailedRequests
		if totalRequests > 0 {
			currentAvg := uas.metrics.AverageResponseTime
			newTime := float64(duration.Milliseconds())
			uas.metrics.AverageResponseTime = (currentAvg*float64(totalRequests-1) + newTime) / float64(totalRequests)
		}

		// 更新分类统计
		uas.metrics.RequestsByIntent[metricType]++
	}

	uas.metrics.LastUpdated = time.Now()
}

// GetMetrics 获取指标
func (uas *UnifiedAIService) GetMetrics() *UnifiedAIMetrics {
	uas.metrics.mutex.RLock()
	defer uas.metrics.mutex.RUnlock()

	// 创建副本避免并发问题
	metricsCopy := &UnifiedAIMetrics{
		TotalRequests:       uas.metrics.TotalRequests,
		SuccessfulRequests:  uas.metrics.SuccessfulRequests,
		FailedRequests:      uas.metrics.FailedRequests,
		CachedRequests:      uas.metrics.CachedRequests,
		AverageResponseTime: uas.metrics.AverageResponseTime,
		P95ResponseTime:     uas.metrics.P95ResponseTime,
		P99ResponseTime:     uas.metrics.P99ResponseTime,
		IntentAccuracy:      uas.metrics.IntentAccuracy,
		FallbackRate:        uas.metrics.FallbackRate,
		SecurityBlockRate:   uas.metrics.SecurityBlockRate,
		RequestsByIntent:    make(map[string]int64),
		RequestsByHandler:   make(map[string]int64),
		ErrorsByType:        make(map[string]int64),
		LastUpdated:         uas.metrics.LastUpdated,
	}

	// 复制映射
	for k, v := range uas.metrics.RequestsByIntent {
		metricsCopy.RequestsByIntent[k] = v
	}
	for k, v := range uas.metrics.RequestsByHandler {
		metricsCopy.RequestsByHandler[k] = v
	}
	for k, v := range uas.metrics.ErrorsByType {
		metricsCopy.ErrorsByType[k] = v
	}

	return metricsCopy
}

// GetSystemStatus 获取系统状态
func (uas *UnifiedAIService) GetSystemStatus() map[string]interface{} {
	return map[string]interface{}{
		"service_status": "running",
		"components": map[string]interface{}{
			"intent_engine":    "healthy",
			"execution_engine": "healthy",
			"security_manager": "healthy",
			"context_manager":  "healthy",
		},
		"config":       uas.config,
		"metrics":      uas.GetMetrics(),
		"handlers":     len(uas.handlers),
		"last_updated": time.Now(),
	}
}
