package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// Config 应用配置结构
type Config struct {
	App      AppConfig      `mapstructure:"app"`
	Database DatabaseConfig `mapstructure:"database"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	DeepSeek DeepSeekConfig `mapstructure:"deepseek"`
	Security SecurityConfig `mapstructure:"security"`
	Redis    RedisConfig    `mapstructure:"redis"`
	SSH      SSHConfig      `mapstructure:"ssh"`
	Log      LogConfig      `mapstructure:"log"`
	Metrics  MetricsConfig  `mapstructure:"metrics"`
	Cache    CacheConfig    `mapstructure:"cache"`
	Agent    AgentConfig    `mapstructure:"agent"`
}

// AppConfig 应用基础配置
type AppConfig struct {
	Name    string `mapstructure:"name"`
	Env     string `mapstructure:"env"`
	Debug   bool   `mapstructure:"debug"`
	Port    int    `mapstructure:"port"`
	Version string `mapstructure:"version"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path            string        `mapstructure:"path"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret                string        `mapstructure:"secret"`
	AccessTokenTTL        time.Duration `mapstructure:"access_token_ttl"`
	RefreshTokenTTL       time.Duration `mapstructure:"refresh_token_ttl"`
	Issuer                string        `mapstructure:"issuer"`
	MaxConcurrentSessions int           `mapstructure:"max_concurrent_sessions"`
}

// DeepSeekConfig DeepSeek API配置
type DeepSeekConfig struct {
	APIKey           string        `mapstructure:"api_key"`
	APIURL           string        `mapstructure:"api_url"`
	Model            string        `mapstructure:"model"`
	Timeout          time.Duration `mapstructure:"timeout"`
	MaxRetries       int           `mapstructure:"max_retries"`
	MaxContextTokens int           `mapstructure:"max_context_tokens"`
	Temperature      float64       `mapstructure:"temperature"`
	TopP             float64       `mapstructure:"top_p"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	EncryptionKey    string          `mapstructure:"encryption_key"`
	PasswordHashCost int             `mapstructure:"password_hash_cost"`
	SessionTimeout   time.Duration   `mapstructure:"session_timeout"`
	RateLimit        RateLimitConfig `mapstructure:"rate_limit"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Global  string `mapstructure:"global"`
	PerUser string `mapstructure:"per_user"`
	PerIP   string `mapstructure:"per_ip"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// SSHConfig SSH配置
type SSHConfig struct {
	Timeout             time.Duration `mapstructure:"timeout"`
	MaxConnections      int           `mapstructure:"max_connections"`
	IdleTimeout         time.Duration `mapstructure:"idle_timeout"`
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level         string `mapstructure:"level"`
	File          string `mapstructure:"file"`
	MaxSize       int    `mapstructure:"max_size"`
	RetentionDays int    `mapstructure:"retention_days"`
	Format        string `mapstructure:"format"`
}

// MetricsConfig 监控配置
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	L1Size  string        `mapstructure:"l1_size"`
	L1TTL   time.Duration `mapstructure:"l1_ttl"`
	L2TTL   time.Duration `mapstructure:"l2_ttl"`
}

// AgentConfig Agent平台配置
type AgentConfig struct {
	Enabled                bool          `mapstructure:"enabled"`                  // 是否启用Agent平台
	MaxAgents              int           `mapstructure:"max_agents"`               // 最大Agent数量
	MaxConcurrentRequests  int           `mapstructure:"max_concurrent_requests"`  // 最大并发请求数
	HealthCheckInterval    time.Duration `mapstructure:"health_check_interval"`    // 健康检查间隔
	RegistrationTTL        time.Duration `mapstructure:"registration_ttl"`         // 注册TTL
	CleanupInterval        time.Duration `mapstructure:"cleanup_interval"`         // 清理间隔
	EnableAutoRegistration bool          `mapstructure:"enable_auto_registration"` // 是否启用自动注册
}

// Load 加载配置
func Load() (*Config, error) {
	// 加载 .env 文件 (如果存在)
	_ = godotenv.Load()

	// 映射旧的环境变量名到新的名称
	mapLegacyEnvVars()

	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 设置环境变量前缀
	viper.SetEnvPrefix("AIOPS")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	return &config, nil
}

// setDefaults 设置默认配置值
func setDefaults() {
	// App defaults
	viper.SetDefault("app.name", "aiops-platform")
	viper.SetDefault("app.env", "development")
	viper.SetDefault("app.debug", true)
	viper.SetDefault("app.port", 8080)
	viper.SetDefault("app.version", "1.0.0")

	// Database defaults
	viper.SetDefault("database.path", "./data/aiops.db")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "1h")
	viper.SetDefault("database.conn_max_idle_time", "10m")

	// JWT defaults
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.access_token_ttl", "15m")
	viper.SetDefault("jwt.refresh_token_ttl", "7d")
	viper.SetDefault("jwt.issuer", "aiops-platform")
	viper.SetDefault("jwt.max_concurrent_sessions", 5)

	// DeepSeek defaults
	viper.SetDefault("deepseek.api_url", "https://api.deepseek.com")
	viper.SetDefault("deepseek.model", "deepseek-chat")
	viper.SetDefault("deepseek.timeout", "30s")
	viper.SetDefault("deepseek.max_retries", 3)
	viper.SetDefault("deepseek.max_context_tokens", 4000)
	viper.SetDefault("deepseek.temperature", 0.7)
	viper.SetDefault("deepseek.top_p", 0.9)

	// Security defaults
	viper.SetDefault("security.encryption_key", "dev-encryption-key-32bytes-long!")
	viper.SetDefault("security.password_hash_cost", 12)
	viper.SetDefault("security.session_timeout", "24h")
	viper.SetDefault("security.rate_limit.enabled", true)
	viper.SetDefault("security.rate_limit.global", "1000/min")
	viper.SetDefault("security.rate_limit.per_user", "100/min")
	viper.SetDefault("security.rate_limit.per_ip", "200/min")

	// Redis defaults
	viper.SetDefault("redis.enabled", false)
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)

	// SSH defaults
	viper.SetDefault("ssh.timeout", "30s")
	viper.SetDefault("ssh.max_connections", 10)
	viper.SetDefault("ssh.idle_timeout", "5m")
	viper.SetDefault("ssh.health_check_interval", "1m")

	// Log defaults
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.file", "./logs/aiops.log")
	viper.SetDefault("log.max_size", 100)
	viper.SetDefault("log.retention_days", 30)
	viper.SetDefault("log.format", "json")

	// Metrics defaults
	viper.SetDefault("metrics.enabled", true)
	viper.SetDefault("metrics.port", 9090)
	viper.SetDefault("metrics.path", "/metrics")

	// Cache defaults
	viper.SetDefault("cache.enabled", true)
	viper.SetDefault("cache.l1_size", "100MB")
	viper.SetDefault("cache.l1_ttl", "5m")
	viper.SetDefault("cache.l2_ttl", "1h")

	// Agent defaults
	viper.SetDefault("agent.enabled", false)
	viper.SetDefault("agent.max_agents", 50)
	viper.SetDefault("agent.max_concurrent_requests", 10)
	viper.SetDefault("agent.health_check_interval", "30s")
	viper.SetDefault("agent.registration_ttl", "1h")
	viper.SetDefault("agent.cleanup_interval", "10m")
	viper.SetDefault("agent.enable_auto_registration", true)
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.DeepSeek.APIKey == "" {
		return fmt.Errorf("deepseek.api_key is required")
	}

	if config.JWT.Secret == "" || config.JWT.Secret == "your-secret-key" {
		return fmt.Errorf("jwt.secret must be set to a secure value")
	}

	// 处理加密密钥的环境变量回退
	if config.Security.EncryptionKey == "" || config.Security.EncryptionKey == "${AIOPS_ENCRYPTION_KEY}" {
		// 如果配置为空或者是未解析的环境变量，使用默认值
		config.Security.EncryptionKey = "dev-encryption-key-32bytes-long!"
	}

	if len(config.Security.EncryptionKey) != 32 {
		return fmt.Errorf("security.encryption_key must be exactly 32 bytes, current length: %d", len(config.Security.EncryptionKey))
	}

	return nil
}

// mapLegacyEnvVars 映射旧的环境变量名到新的名称
func mapLegacyEnvVars() {
	// 映射旧的环境变量名到新的AIOPS_前缀名称
	envMappings := map[string]string{
		"DEEPSEEK_API_KEY": "AIOPS_DEEPSEEK_API_KEY",
		"JWT_SECRET":       "AIOPS_JWT_SECRET",
		"ENCRYPTION_KEY":   "AIOPS_ENCRYPTION_KEY",
	}

	for oldName, newName := range envMappings {
		if oldValue := os.Getenv(oldName); oldValue != "" && os.Getenv(newName) == "" {
			os.Setenv(newName, oldValue)
		}
	}
}
