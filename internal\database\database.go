package database

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// New 创建新的数据库连接
func New(cfg config.DatabaseConfig) (*gorm.DB, error) {
	// 确保数据库目录存在
	dbDir := filepath.Dir(cfg.Path)
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	}

	// 打开数据库连接
	db, err := gorm.Open(sqlite.Open(cfg.Path), gormConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// 获取底层SQL DB
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	// 配置连接池
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)
	sqlDB.SetConnMaxIdleTime(cfg.ConnMaxIdleTime)

	// 设置SQLite特定的PRAGMA
	if err := setPragmas(db); err != nil {
		return nil, fmt.Errorf("failed to set SQLite pragmas: %w", err)
	}

	return db, nil
}

// setPragmas 设置SQLite性能优化参数
func setPragmas(db *gorm.DB) error {
	pragmas := []string{
		"PRAGMA journal_mode = WAL",    // 启用WAL模式
		"PRAGMA synchronous = NORMAL",  // 平衡性能和安全性
		"PRAGMA cache_size = -64000",   // 64MB缓存
		"PRAGMA temp_store = MEMORY",   // 临时表存储在内存
		"PRAGMA mmap_size = 268435456", // 256MB内存映射
		"PRAGMA foreign_keys = ON",     // 启用外键约束
		"PRAGMA busy_timeout = 30000",  // 30秒忙等待超时
	}

	for _, pragma := range pragmas {
		if err := db.Exec(pragma).Error; err != nil {
			return fmt.Errorf("failed to execute pragma %s: %w", pragma, err)
		}
	}

	return nil
}

// Migrate 执行数据库迁移
func Migrate(db *gorm.DB) error {
	// 自动迁移所有模型
	models := []interface{}{
		&model.User{},
		&model.Host{},
		&model.Alert{},
		&model.OperationLog{},
		&model.ChatSession{},
		&model.ChatMessage{},
		&model.Permission{},
		&model.SystemConfig{},
		&model.DatabaseVersion{},
		// 工作流相关模型
		&model.WorkflowDefinition{},
		&model.WorkflowInstance{},
		&model.WorkflowEvent{},
		&model.WorkflowStepExecution{},
		&model.WorkflowTemplate{},
		&model.WorkflowStatistics{},
		&model.WorkflowUserPreference{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate model %T: %w", model, err)
		}
	}

	// 创建索引
	if err := createIndexes(db); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// 插入初始数据
	if err := seedData(db); err != nil {
		return fmt.Errorf("failed to seed data: %w", err)
	}

	// 更新数据库版本
	if err := updateVersion(db, 1); err != nil {
		return fmt.Errorf("failed to update database version: %w", err)
	}

	return nil
}

// createIndexes 创建数据库索引
func createIndexes(db *gorm.DB) error {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
		"CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
		"CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)",

		"CREATE INDEX IF NOT EXISTS idx_hosts_name ON hosts(name)",
		"CREATE INDEX IF NOT EXISTS idx_hosts_ip ON hosts(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_hosts_status ON hosts(status)",
		"CREATE INDEX IF NOT EXISTS idx_hosts_group ON hosts(group_name)",

		"CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status)",
		"CREATE INDEX IF NOT EXISTS idx_alerts_level ON alerts(level)",
		"CREATE INDEX IF NOT EXISTS idx_alerts_time ON alerts(alert_time)",
		"CREATE INDEX IF NOT EXISTS idx_alerts_host ON alerts(host_id)",

		"CREATE INDEX IF NOT EXISTS idx_operations_type ON operations_log(operation_type)",
		"CREATE INDEX IF NOT EXISTS idx_operations_status ON operations_log(status)",
		"CREATE INDEX IF NOT EXISTS idx_operations_time ON operations_log(executed_at)",
		"CREATE INDEX IF NOT EXISTS idx_operations_user ON operations_log(user_id)",

		"CREATE INDEX IF NOT EXISTS idx_chat_sessions_user ON chat_sessions(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_chat_sessions_status ON chat_sessions(status)",
		"CREATE INDEX IF NOT EXISTS idx_chat_sessions_activity ON chat_sessions(last_activity)",

		"CREATE INDEX IF NOT EXISTS idx_chat_messages_session ON chat_messages(session_id)",
		"CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type)",
		"CREATE INDEX IF NOT EXISTS idx_chat_messages_time ON chat_messages(created_at)",

		"CREATE INDEX IF NOT EXISTS idx_permissions_resource ON permissions(resource)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_action ON permissions(action)",
		"CREATE INDEX IF NOT EXISTS idx_permissions_scope ON permissions(scope)",

		"CREATE INDEX IF NOT EXISTS idx_config_key ON system_config(config_key)",
		"CREATE INDEX IF NOT EXISTS idx_config_category ON system_config(category)",
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			return fmt.Errorf("failed to create index: %s, error: %w", index, err)
		}
	}

	return nil
}

// seedData 插入初始数据
func seedData(db *gorm.DB) error {
	// 权限初始化现在由RBAC服务处理

	// 检查系统配置表是否已有数据
	var configCount int64
	if err := db.Model(&model.SystemConfig{}).Count(&configCount).Error; err != nil {
		return err
	}

	// 只有配置表为空时才插入配置数据
	if configCount == 0 {
		// 插入默认系统配置
		configs := []model.SystemConfig{
			{ConfigKey: "deepseek_api_url", ConfigValue: "https://api.deepseek.com", ConfigType: "string", Description: "DeepSeek API地址", Category: "ai"},
			{ConfigKey: "deepseek_model", ConfigValue: "deepseek-chat", ConfigType: "string", Description: "DeepSeek模型名称", Category: "ai"},
			{ConfigKey: "max_context_tokens", ConfigValue: "4000", ConfigType: "number", Description: "最大上下文Token数", Category: "ai"},
			{ConfigKey: "ssh_timeout", ConfigValue: "30", ConfigType: "number", Description: "SSH连接超时时间(秒)", Category: "ssh"},
			{ConfigKey: "max_ssh_connections", ConfigValue: "10", ConfigType: "number", Description: "最大SSH连接数", Category: "ssh"},
			{ConfigKey: "alert_retention_days", ConfigValue: "90", ConfigType: "number", Description: "告警保留天数", Category: "monitoring"},
			{ConfigKey: "log_retention_days", ConfigValue: "30", ConfigType: "number", Description: "日志保留天数", Category: "logging"},
			{ConfigKey: "session_timeout", ConfigValue: "24", ConfigType: "number", Description: "会话超时时间(小时)", Category: "security"},
		}

		for _, config := range configs {
			if err := db.Create(&config).Error; err != nil {
				return fmt.Errorf("failed to create system config: %w", err)
			}
		}
	}

	// 检查用户表是否已有数据
	var userCount int64
	if err := db.Model(&model.User{}).Count(&userCount).Error; err != nil {
		return err
	}

	// 只有用户表为空时才插入默认用户
	if userCount == 0 {
		// 插入默认用户
		defaultUser := model.User{
			ID:           1,
			Username:     "admin",
			PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Email:        "<EMAIL>",
			FullName:     "系统管理员",
			Role:         "super_admin",
			IsActive:     true,
		}

		if err := db.Create(&defaultUser).Error; err != nil {
			return fmt.Errorf("failed to create default user: %w", err)
		}
	}

	return nil
}

// Rollback 回滚数据库
func Rollback(db *gorm.DB) error {
	// 删除所有表
	models := []interface{}{
		&model.DatabaseVersion{},
		&model.SystemConfig{},
		&model.Permission{},
		&model.ChatMessage{},
		&model.ChatSession{},
		&model.OperationLog{},
		&model.Alert{},
		&model.Host{},
		&model.User{},
	}

	for _, model := range models {
		if err := db.Migrator().DropTable(model); err != nil {
			return fmt.Errorf("failed to drop table for model %T: %w", model, err)
		}
	}

	return nil
}

// Reset 重置数据库
func Reset(db *gorm.DB) error {
	if err := Rollback(db); err != nil {
		return err
	}
	return Migrate(db)
}

// GetVersion 获取数据库版本
func GetVersion(db *gorm.DB) (int, error) {
	var version model.DatabaseVersion
	err := db.Order("version DESC").First(&version).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, nil
		}
		return 0, err
	}
	return version.Version, nil
}

// updateVersion 更新数据库版本
func updateVersion(db *gorm.DB, version int) error {
	dbVersion := model.DatabaseVersion{
		Version:   version,
		AppliedAt: time.Now(),
	}
	return db.Create(&dbVersion).Error
}
