package security

import (
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 使用统一的风险等级类型，添加安全级别
const (
	RiskLevelSafe RiskLevel = "safe" // 安全命令
)

// CommandSecurityRule 命令安全规则
type CommandSecurityRule struct {
	Pattern     string    `json:"pattern"`      // 正则表达式模式
	RiskLevel   RiskLevel `json:"risk_level"`   // 风险等级
	Description string    `json:"description"`  // 风险描述
	Suggestion  string    `json:"suggestion"`   // 安全建议
	RequireAuth bool      `json:"require_auth"` // 是否需要额外认证
}

// CommandSecurityResult 命令安全检测结果
type CommandSecurityResult struct {
	Command         string                `json:"command"`
	RiskLevel       RiskLevel             `json:"risk_level"`
	IsAllowed       bool                  `json:"is_allowed"`
	MatchedRules    []CommandSecurityRule `json:"matched_rules"`
	SecurityWarning string                `json:"security_warning"`
	RequireAuth     bool                  `json:"require_auth"`
	Suggestions     []string              `json:"suggestions"`
}

// CommandSecurityChecker 命令安全检查器
type CommandSecurityChecker struct {
	rules  []CommandSecurityRule
	logger *logrus.Logger
}

// NewCommandSecurityChecker 创建命令安全检查器
func NewCommandSecurityChecker(logger *logrus.Logger) *CommandSecurityChecker {
	checker := &CommandSecurityChecker{
		logger: logger,
	}
	checker.initializeSecurityRules()
	return checker
}

// initializeSecurityRules 初始化安全规则
func (csc *CommandSecurityChecker) initializeSecurityRules() {
	csc.rules = []CommandSecurityRule{
		// 极高风险命令
		{
			Pattern:     `rm\s+(-rf|--recursive\s+--force|\-r\s+\-f)\s+/`,
			RiskLevel:   RiskLevelCritical,
			Description: "递归删除根目录或系统目录",
			Suggestion:  "请确认删除路径，避免删除系统关键目录",
			RequireAuth: true,
		},
		{
			Pattern:     `dd\s+.*of=/dev/(sd[a-z]|hd[a-z]|nvme[0-9])`,
			RiskLevel:   RiskLevelCritical,
			Description: "直接写入磁盘设备",
			Suggestion:  "此操作可能损坏磁盘数据，请谨慎操作",
			RequireAuth: true,
		},
		{
			Pattern:     `mkfs\.(ext[234]|xfs|btrfs)`,
			RiskLevel:   RiskLevelCritical,
			Description: "格式化文件系统",
			Suggestion:  "格式化操作将清除所有数据，请确认操作",
			RequireAuth: true,
		},
		{
			Pattern:     `fdisk|parted|gdisk`,
			RiskLevel:   RiskLevelCritical,
			Description: "磁盘分区操作",
			Suggestion:  "分区操作可能影响系统启动，请谨慎操作",
			RequireAuth: true,
		},

		// 高风险命令
		{
			Pattern:     `rm\s+(-rf|--recursive\s+--force|\-r\s+\-f)`,
			RiskLevel:   RiskLevelHigh,
			Description: "递归强制删除文件或目录",
			Suggestion:  "请确认删除路径，建议先备份重要数据",
			RequireAuth: true,
		},
		{
			Pattern:     `chmod\s+777`,
			RiskLevel:   RiskLevelHigh,
			Description: "设置最大权限（777）",
			Suggestion:  "777权限存在安全风险，建议使用更安全的权限设置",
			RequireAuth: false,
		},
		{
			Pattern:     `chown\s+.*root`,
			RiskLevel:   RiskLevelHigh,
			Description: "更改文件所有者为root",
			Suggestion:  "请确认是否需要root权限，避免权限提升风险",
			RequireAuth: false,
		},
		{
			Pattern:     `systemctl\s+(stop|disable)\s+(ssh|sshd|network|firewall)`,
			RiskLevel:   RiskLevelHigh,
			Description: "停止关键系统服务",
			Suggestion:  "停止SSH或网络服务可能导致连接中断",
			RequireAuth: true,
		},
		{
			Pattern:     `iptables\s+(-F|--flush)`,
			RiskLevel:   RiskLevelHigh,
			Description: "清空防火墙规则",
			Suggestion:  "清空防火墙规则可能带来安全风险",
			RequireAuth: true,
		},

		// 中风险命令
		{
			Pattern:     `kill\s+(-9|--kill)\s+1`,
			RiskLevel:   RiskLevelMedium,
			Description: "强制终止init进程",
			Suggestion:  "终止init进程可能导致系统重启",
			RequireAuth: false,
		},
		{
			Pattern:     `mount|umount`,
			RiskLevel:   RiskLevelMedium,
			Description: "挂载或卸载文件系统",
			Suggestion:  "请确认挂载点和设备正确",
			RequireAuth: false,
		},
		{
			Pattern:     `crontab\s+(-r|--remove)`,
			RiskLevel:   RiskLevelMedium,
			Description: "删除定时任务",
			Suggestion:  "删除定时任务前请确认影响范围",
			RequireAuth: false,
		},
		{
			Pattern:     `passwd\s+root`,
			RiskLevel:   RiskLevelMedium,
			Description: "修改root密码",
			Suggestion:  "修改root密码请确保新密码安全",
			RequireAuth: false,
		},

		// 低风险命令
		{
			Pattern:     `wget|curl.*\|\s*(sh|bash)`,
			RiskLevel:   RiskLevelLow,
			Description: "下载并执行脚本",
			Suggestion:  "执行网络脚本存在安全风险，请确认脚本来源",
			RequireAuth: false,
		},
		{
			Pattern:     `sudo\s+`,
			RiskLevel:   RiskLevelLow,
			Description: "使用sudo提权执行",
			Suggestion:  "sudo命令需要谨慎使用",
			RequireAuth: false,
		},
	}
}

// CheckCommand 检查命令安全性
func (csc *CommandSecurityChecker) CheckCommand(command string) *CommandSecurityResult {
	result := &CommandSecurityResult{
		Command:      command,
		RiskLevel:    RiskLevelSafe,
		IsAllowed:    true,
		MatchedRules: make([]CommandSecurityRule, 0),
		Suggestions:  make([]string, 0),
	}

	// 标准化命令（去除多余空格）
	normalizedCmd := strings.TrimSpace(regexp.MustCompile(`\s+`).ReplaceAllString(command, " "))

	// 检查每个安全规则
	for _, rule := range csc.rules {
		matched, err := regexp.MatchString("(?i)"+rule.Pattern, normalizedCmd)
		if err != nil {
			csc.logger.WithError(err).Warn("Failed to match security rule pattern")
			continue
		}

		if matched {
			result.MatchedRules = append(result.MatchedRules, rule)
			result.Suggestions = append(result.Suggestions, rule.Suggestion)

			// 更新风险等级（取最高风险等级）
			if csc.isHigherRisk(rule.RiskLevel, result.RiskLevel) {
				result.RiskLevel = rule.RiskLevel
			}

			// 如果任何规则需要认证，则需要认证
			if rule.RequireAuth {
				result.RequireAuth = true
			}
		}
	}

	// 根据风险等级决定是否允许执行
	result.IsAllowed = csc.isCommandAllowed(result.RiskLevel)
	result.SecurityWarning = csc.generateSecurityWarning(result)

	csc.logger.WithFields(logrus.Fields{
		"command":       command,
		"risk_level":    result.RiskLevel,
		"is_allowed":    result.IsAllowed,
		"rules_matched": len(result.MatchedRules),
	}).Info("Command security check completed")

	return result
}

// isHigherRisk 判断风险等级是否更高
func (csc *CommandSecurityChecker) isHigherRisk(newLevel, currentLevel RiskLevel) bool {
	riskOrder := map[RiskLevel]int{
		RiskLevelSafe:     0,
		RiskLevelLow:      1,
		RiskLevelMedium:   2,
		RiskLevelHigh:     3,
		RiskLevelCritical: 4,
	}
	return riskOrder[newLevel] > riskOrder[currentLevel]
}

// isCommandAllowed 判断命令是否允许执行
func (csc *CommandSecurityChecker) isCommandAllowed(riskLevel RiskLevel) bool {
	// 可以根据配置调整允许的风险等级
	switch riskLevel {
	case RiskLevelSafe, RiskLevelLow:
		return true
	case RiskLevelMedium:
		return true // 中风险命令允许执行，但需要警告
	case RiskLevelHigh:
		return false // 高风险命令需要额外确认
	case RiskLevelCritical:
		return false // 极高风险命令禁止执行
	default:
		return true
	}
}

// generateSecurityWarning 生成安全警告信息
func (csc *CommandSecurityChecker) generateSecurityWarning(result *CommandSecurityResult) string {
	if result.RiskLevel == RiskLevelSafe {
		return ""
	}

	var warning strings.Builder
	warning.WriteString(fmt.Sprintf("⚠️ **安全警告**: 检测到%s风险命令\n\n", csc.getRiskLevelText(result.RiskLevel)))

	if len(result.MatchedRules) > 0 {
		warning.WriteString("**风险详情**:\n")
		for i, rule := range result.MatchedRules {
			warning.WriteString(fmt.Sprintf("%d. %s\n", i+1, rule.Description))
		}
		warning.WriteString("\n")
	}

	if len(result.Suggestions) > 0 {
		warning.WriteString("**安全建议**:\n")
		for i, suggestion := range result.Suggestions {
			warning.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return warning.String()
}

// getRiskLevelText 获取风险等级文本
func (csc *CommandSecurityChecker) getRiskLevelText(level RiskLevel) string {
	switch level {
	case RiskLevelLow:
		return "低"
	case RiskLevelMedium:
		return "中"
	case RiskLevelHigh:
		return "高"
	case RiskLevelCritical:
		return "极高"
	default:
		return "未知"
	}
}

// CommandAuditLog 命令审计日志
type CommandAuditLog struct {
	ID         int64      `json:"id"`
	UserID     int64      `json:"user_id"`
	HostID     int64      `json:"host_id"`
	Command    string     `json:"command"`
	RiskLevel  RiskLevel  `json:"risk_level"`
	IsAllowed  bool       `json:"is_allowed"`
	ExecutedAt *time.Time `json:"executed_at"`
	Result     string     `json:"result"`
	ExitCode   int        `json:"exit_code"`
	Duration   int        `json:"duration"`
	IPAddress  string     `json:"ip_address"`
	UserAgent  string     `json:"user_agent"`
	SessionID  string     `json:"session_id"`
	CreatedAt  time.Time  `json:"created_at"`
}
