package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ApprovalManager 审批管理器
type ApprovalManager struct {
	db         *gorm.DB
	logger     *logrus.Logger
	wsManager  *WebSocketManager
	riskEngine *RiskEngine
	approvals  map[string]*ApprovalRequest
	approvers  map[int64]*ApproverProfile
}

// ApprovalRequest 审批请求
type ApprovalRequest struct {
	ID                string                   `json:"id"`
	UserID            int64                    `json:"user_id"`
	HostID            int64                    `json:"host_id"`
	Command           string                   `json:"command"`
	RiskAssessment    *RiskAssessmentResult    `json:"risk_assessment"`
	RequestedAt       time.Time                `json:"requested_at"`
	ExpiresAt         time.Time                `json:"expires_at"`
	Status            ApprovalStatus           `json:"status"`
	RequiredApprovers []int64                  `json:"required_approvers"`
	Approvals         []*Approval              `json:"approvals"`
	Rejections        []*Rejection             `json:"rejections"`
	AutoApprove       bool                     `json:"auto_approve"`
	Context           *CommandExecutionContext `json:"context"`
	Metadata          map[string]interface{}   `json:"metadata"`
}

// Approval 审批记录
type Approval struct {
	ApproverID int64     `json:"approver_id"`
	ApprovedAt time.Time `json:"approved_at"`
	Comment    string    `json:"comment"`
	Conditions []string  `json:"conditions"`
}

// Rejection 拒绝记录
type Rejection struct {
	ApproverID int64     `json:"approver_id"`
	RejectedAt time.Time `json:"rejected_at"`
	Reason     string    `json:"reason"`
	Comment    string    `json:"comment"`
}

// ApprovalStatus 审批状态
type ApprovalStatus string

const (
	ApprovalStatusPending   ApprovalStatus = "pending"
	ApprovalStatusApproved  ApprovalStatus = "approved"
	ApprovalStatusRejected  ApprovalStatus = "rejected"
	ApprovalStatusExpired   ApprovalStatus = "expired"
	ApprovalStatusCancelled ApprovalStatus = "cancelled"
)

// ApproverProfile 审批者档案
type ApproverProfile struct {
	UserID       int64     `json:"user_id"`
	Role         string    `json:"role"`
	Permissions  []string  `json:"permissions"`
	MaxRiskLevel RiskLevel `json:"max_risk_level"`
	AutoApprove  bool      `json:"auto_approve"`
	Enabled      bool      `json:"enabled"`
}

// ApprovalResponse 审批响应
type ApprovalResponse struct {
	RequestID  string         `json:"request_id"`
	Status     ApprovalStatus `json:"status"`
	Message    string         `json:"message"`
	CanExecute bool           `json:"can_execute"`
	Conditions []string       `json:"conditions"`
	ExpiresAt  *time.Time     `json:"expires_at,omitempty"`
}

// ApprovalDecision 审批决定
type ApprovalDecision struct {
	RequestID  string   `json:"request_id"`
	ApproverID int64    `json:"approver_id"`
	Decision   string   `json:"decision"` // approve, reject
	Comment    string   `json:"comment"`
	Conditions []string `json:"conditions"`
}

// NewApprovalManager 创建审批管理器
func NewApprovalManager(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager, riskEngine *RiskEngine) *ApprovalManager {
	manager := &ApprovalManager{
		db:         db,
		logger:     logger,
		wsManager:  wsManager,
		riskEngine: riskEngine,
		approvals:  make(map[string]*ApprovalRequest),
		approvers:  make(map[int64]*ApproverProfile),
	}

	// 初始化默认审批者
	manager.initializeDefaultApprovers()

	// 启动清理例程
	go manager.startCleanupRoutine(context.Background())

	return manager
}

// initializeDefaultApprovers 初始化默认审批者
func (am *ApprovalManager) initializeDefaultApprovers() {
	// 这里可以从数据库加载审批者配置
	// 暂时使用硬编码的默认配置
	defaultApprovers := []*ApproverProfile{
		{
			UserID:       1, // 假设用户ID 1是管理员
			Role:         "admin",
			Permissions:  []string{"approve_all", "reject_all"},
			MaxRiskLevel: RiskLevelCritical,
			AutoApprove:  false,
			Enabled:      true,
		},
		{
			UserID:       2, // 假设用户ID 2是高级运维
			Role:         "senior_ops",
			Permissions:  []string{"approve_medium", "approve_high"},
			MaxRiskLevel: RiskLevelHigh,
			AutoApprove:  false,
			Enabled:      true,
		},
	}

	for _, approver := range defaultApprovers {
		am.approvers[approver.UserID] = approver
	}

	am.logger.WithField("approver_count", len(am.approvers)).Info("Default approvers initialized")
}

// RequestApproval 请求审批
func (am *ApprovalManager) RequestApproval(ctx context.Context, userID, hostID int64, command string, riskAssessment *RiskAssessmentResult, execCtx *CommandExecutionContext) (*ApprovalResponse, error) {
	// 创建审批请求
	request := &ApprovalRequest{
		ID:                uuid.New().String(),
		UserID:            userID,
		HostID:            hostID,
		Command:           command,
		RiskAssessment:    riskAssessment,
		RequestedAt:       time.Now(),
		ExpiresAt:         time.Now().Add(30 * time.Minute), // 默认30分钟过期
		Status:            ApprovalStatusPending,
		RequiredApprovers: am.getRequiredApprovers(riskAssessment),
		Approvals:         make([]*Approval, 0),
		Rejections:        make([]*Rejection, 0),
		AutoApprove:       false,
		Context:           execCtx,
		Metadata:          make(map[string]interface{}),
	}

	// 检查是否可以自动审批
	if am.canAutoApprove(request) {
		request.Status = ApprovalStatusApproved
		request.AutoApprove = true

		am.logger.WithFields(logrus.Fields{
			"request_id": request.ID,
			"user_id":    userID,
			"command":    command,
		}).Info("Request auto-approved")

		return &ApprovalResponse{
			RequestID:  request.ID,
			Status:     ApprovalStatusApproved,
			Message:    "请求已自动审批",
			CanExecute: true,
		}, nil
	}

	// 存储审批请求
	am.approvals[request.ID] = request

	// 发送审批通知
	am.sendApprovalNotifications(request)

	// 发送用户通知
	am.sendUserNotification(request)

	am.logger.WithFields(logrus.Fields{
		"request_id":         request.ID,
		"user_id":            userID,
		"host_id":            hostID,
		"command":            command,
		"risk_level":         riskAssessment.OverallRisk,
		"required_approvers": len(request.RequiredApprovers),
	}).Info("Approval request created")

	return &ApprovalResponse{
		RequestID:  request.ID,
		Status:     ApprovalStatusPending,
		Message:    "请求已提交，等待审批",
		CanExecute: false,
		ExpiresAt:  &request.ExpiresAt,
	}, nil
}

// ProcessApprovalDecision 处理审批决定
func (am *ApprovalManager) ProcessApprovalDecision(ctx context.Context, decision *ApprovalDecision) (*ApprovalResponse, error) {
	request, exists := am.approvals[decision.RequestID]
	if !exists {
		return nil, fmt.Errorf("approval request not found: %s", decision.RequestID)
	}

	if request.Status != ApprovalStatusPending {
		return nil, fmt.Errorf("request is not pending: %s", request.Status)
	}

	// 检查审批者权限
	approver, exists := am.approvers[decision.ApproverID]
	if !exists || !approver.Enabled {
		return nil, fmt.Errorf("approver not found or disabled: %d", decision.ApproverID)
	}

	// 检查审批者是否有权限审批此风险等级
	if !am.canApproveRiskLevel(approver, request.RiskAssessment.OverallRisk) {
		return nil, fmt.Errorf("approver does not have permission for risk level: %s", request.RiskAssessment.OverallRisk)
	}

	switch decision.Decision {
	case "approve":
		approval := &Approval{
			ApproverID: decision.ApproverID,
			ApprovedAt: time.Now(),
			Comment:    decision.Comment,
			Conditions: decision.Conditions,
		}
		request.Approvals = append(request.Approvals, approval)

		// 检查是否满足审批条件
		if am.isApprovalComplete(request) {
			request.Status = ApprovalStatusApproved
			am.sendApprovalCompleteNotification(request)
		}

	case "reject":
		rejection := &Rejection{
			ApproverID: decision.ApproverID,
			RejectedAt: time.Now(),
			Reason:     "Manual rejection",
			Comment:    decision.Comment,
		}
		request.Rejections = append(request.Rejections, rejection)
		request.Status = ApprovalStatusRejected
		am.sendApprovalRejectedNotification(request)

	default:
		return nil, fmt.Errorf("invalid decision: %s", decision.Decision)
	}

	am.logger.WithFields(logrus.Fields{
		"request_id":  decision.RequestID,
		"approver_id": decision.ApproverID,
		"decision":    decision.Decision,
		"status":      request.Status,
	}).Info("Approval decision processed")

	response := &ApprovalResponse{
		RequestID:  request.ID,
		Status:     request.Status,
		CanExecute: request.Status == ApprovalStatusApproved,
	}

	switch request.Status {
	case ApprovalStatusApproved:
		response.Message = "请求已审批通过"
		// 收集所有条件
		conditions := make([]string, 0)
		for _, approval := range request.Approvals {
			conditions = append(conditions, approval.Conditions...)
		}
		response.Conditions = conditions
	case ApprovalStatusRejected:
		response.Message = "请求已被拒绝"
	}

	return response, nil
}

// GetApprovalStatus 获取审批状态
func (am *ApprovalManager) GetApprovalStatus(requestID string) (*ApprovalResponse, error) {
	request, exists := am.approvals[requestID]
	if !exists {
		return nil, fmt.Errorf("approval request not found: %s", requestID)
	}

	// 检查是否过期
	if request.Status == ApprovalStatusPending && time.Now().After(request.ExpiresAt) {
		request.Status = ApprovalStatusExpired
		am.sendApprovalExpiredNotification(request)
	}

	response := &ApprovalResponse{
		RequestID:  request.ID,
		Status:     request.Status,
		CanExecute: request.Status == ApprovalStatusApproved,
	}

	switch request.Status {
	case ApprovalStatusPending:
		response.Message = "等待审批中"
		response.ExpiresAt = &request.ExpiresAt
	case ApprovalStatusApproved:
		response.Message = "已审批通过"
	case ApprovalStatusRejected:
		response.Message = "已被拒绝"
	case ApprovalStatusExpired:
		response.Message = "审批已过期"
	}

	return response, nil
}

// getRequiredApprovers 获取所需的审批者
func (am *ApprovalManager) getRequiredApprovers(riskAssessment *RiskAssessmentResult) []int64 {
	requiredApprovers := make([]int64, 0)

	for userID, approver := range am.approvers {
		if !approver.Enabled {
			continue
		}

		if am.canApproveRiskLevel(approver, riskAssessment.OverallRisk) {
			requiredApprovers = append(requiredApprovers, userID)
		}
	}

	return requiredApprovers
}

// canAutoApprove 检查是否可以自动审批
func (am *ApprovalManager) canAutoApprove(request *ApprovalRequest) bool {
	// 低风险命令可以自动审批
	if request.RiskAssessment.OverallRisk <= RiskLevelLow {
		return true
	}

	// 检查是否有自动审批的审批者
	for _, approverID := range request.RequiredApprovers {
		if approver, exists := am.approvers[approverID]; exists && approver.AutoApprove {
			if am.canApproveRiskLevel(approver, request.RiskAssessment.OverallRisk) {
				return true
			}
		}
	}

	return false
}

// canApproveRiskLevel 检查审批者是否可以审批指定风险等级
func (am *ApprovalManager) canApproveRiskLevel(approver *ApproverProfile, riskLevel RiskLevel) bool {
	approverLevel := approver.MaxRiskLevel

	levels := map[RiskLevel]int{
		RiskLevelSafe:     0,
		RiskLevelLow:      1,
		RiskLevelMedium:   2,
		RiskLevelHigh:     3,
		RiskLevelCritical: 4,
	}

	return levels[approverLevel] >= levels[riskLevel]
}

// isApprovalComplete 检查审批是否完成
func (am *ApprovalManager) isApprovalComplete(request *ApprovalRequest) bool {
	// 简单策略：至少需要一个有权限的审批者同意
	for _, approval := range request.Approvals {
		if approver, exists := am.approvers[approval.ApproverID]; exists {
			if am.canApproveRiskLevel(approver, request.RiskAssessment.OverallRisk) {
				return true
			}
		}
	}
	return false
}

// sendApprovalNotifications 发送审批通知
func (am *ApprovalManager) sendApprovalNotifications(request *ApprovalRequest) {
	for _, approverID := range request.RequiredApprovers {
		notification := &SystemNotificationMessage{
			Level:   "warning",
			Title:   "待审批请求",
			Content: fmt.Sprintf("用户 %d 请求在主机 %d 上执行命令: %s", request.UserID, request.HostID, request.Command),
			Actions: []struct {
				ID    string `json:"id"`
				Label string `json:"label"`
				Type  string `json:"type"`
			}{
				{ID: "approve", Label: "批准", Type: "success"},
				{ID: "reject", Label: "拒绝", Type: "danger"},
				{ID: "details", Label: "查看详情", Type: "info"},
			},
		}

		if am.wsManager != nil {
			am.wsManager.SendSystemNotification(approverID, notification)
		}
	}
}

// sendUserNotification 发送用户通知
func (am *ApprovalManager) sendUserNotification(request *ApprovalRequest) {
	notification := &SystemNotificationMessage{
		Level:   "info",
		Title:   "审批请求已提交",
		Content: fmt.Sprintf("您的命令执行请求已提交审批: %s", request.Command),
	}

	if am.wsManager != nil {
		am.wsManager.SendSystemNotification(request.UserID, notification)
	}
}

// sendApprovalCompleteNotification 发送审批完成通知
func (am *ApprovalManager) sendApprovalCompleteNotification(request *ApprovalRequest) {
	notification := &SystemNotificationMessage{
		Level:   "success",
		Title:   "审批已通过",
		Content: fmt.Sprintf("您的命令执行请求已获得批准: %s", request.Command),
	}

	if am.wsManager != nil {
		am.wsManager.SendSystemNotification(request.UserID, notification)
	}
}

// sendApprovalRejectedNotification 发送审批拒绝通知
func (am *ApprovalManager) sendApprovalRejectedNotification(request *ApprovalRequest) {
	notification := &SystemNotificationMessage{
		Level:   "error",
		Title:   "审批已拒绝",
		Content: fmt.Sprintf("您的命令执行请求已被拒绝: %s", request.Command),
	}

	if am.wsManager != nil {
		am.wsManager.SendSystemNotification(request.UserID, notification)
	}
}

// sendApprovalExpiredNotification 发送审批过期通知
func (am *ApprovalManager) sendApprovalExpiredNotification(request *ApprovalRequest) {
	notification := &SystemNotificationMessage{
		Level:   "warning",
		Title:   "审批已过期",
		Content: fmt.Sprintf("您的命令执行请求已过期: %s", request.Command),
	}

	if am.wsManager != nil {
		am.wsManager.SendSystemNotification(request.UserID, notification)
	}
}

// startCleanupRoutine 启动清理例程
func (am *ApprovalManager) startCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			am.logger.Info("Approval cleanup routine stopped")
			return
		case <-ticker.C:
			am.cleanupExpiredRequests()
		}
	}
}

// cleanupExpiredRequests 清理过期请求
func (am *ApprovalManager) cleanupExpiredRequests() {
	now := time.Now()
	expiredCount := 0

	for id, request := range am.approvals {
		if request.Status == ApprovalStatusPending && now.After(request.ExpiresAt) {
			request.Status = ApprovalStatusExpired
			am.sendApprovalExpiredNotification(request)
		}

		// 清理已完成的旧请求（保留24小时）
		if request.Status != ApprovalStatusPending && now.Sub(request.RequestedAt) > 24*time.Hour {
			delete(am.approvals, id)
			expiredCount++
		}
	}

	if expiredCount > 0 {
		am.logger.WithField("expired_count", expiredCount).Info("Cleaned up expired approval requests")
	}
}

// GetPendingApprovals 获取待审批请求
func (am *ApprovalManager) GetPendingApprovals(approverID int64) []*ApprovalRequest {
	pending := make([]*ApprovalRequest, 0)

	for _, request := range am.approvals {
		if request.Status != ApprovalStatusPending {
			continue
		}

		// 检查是否是此审批者的待审批请求
		for _, requiredApproverID := range request.RequiredApprovers {
			if requiredApproverID == approverID {
				pending = append(pending, request)
				break
			}
		}
	}

	return pending
}

// GetApprovalHistory 获取审批历史
func (am *ApprovalManager) GetApprovalHistory(userID int64, limit int) []*ApprovalRequest {
	history := make([]*ApprovalRequest, 0)

	for _, request := range am.approvals {
		if request.UserID == userID {
			history = append(history, request)
		}
	}

	// 简单排序（按请求时间倒序）
	// 在实际实现中，应该使用更高效的排序和分页
	if len(history) > limit {
		history = history[:limit]
	}

	return history
}

// AddApprover 添加审批者
func (am *ApprovalManager) AddApprover(profile *ApproverProfile) error {
	am.approvers[profile.UserID] = profile

	am.logger.WithFields(logrus.Fields{
		"user_id":        profile.UserID,
		"role":           profile.Role,
		"max_risk_level": profile.MaxRiskLevel,
	}).Info("Approver added")

	return nil
}

// RemoveApprover 移除审批者
func (am *ApprovalManager) RemoveApprover(userID int64) error {
	delete(am.approvers, userID)

	am.logger.WithField("user_id", userID).Info("Approver removed")

	return nil
}

// GetApprovalStatistics 获取审批统计
func (am *ApprovalManager) GetApprovalStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"total_requests":    len(am.approvals),
		"pending_requests":  0,
		"approved_requests": 0,
		"rejected_requests": 0,
		"expired_requests":  0,
		"total_approvers":   len(am.approvers),
		"active_approvers":  0,
	}

	for _, request := range am.approvals {
		switch request.Status {
		case ApprovalStatusPending:
			stats["pending_requests"] = stats["pending_requests"].(int) + 1
		case ApprovalStatusApproved:
			stats["approved_requests"] = stats["approved_requests"].(int) + 1
		case ApprovalStatusRejected:
			stats["rejected_requests"] = stats["rejected_requests"].(int) + 1
		case ApprovalStatusExpired:
			stats["expired_requests"] = stats["expired_requests"].(int) + 1
		}
	}

	for _, approver := range am.approvers {
		if approver.Enabled {
			stats["active_approvers"] = stats["active_approvers"].(int) + 1
		}
	}

	return stats
}
