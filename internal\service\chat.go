package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// chatService 对话服务实现
type chatService struct {
	db                       *gorm.DB
	config                   *config.Config
	logger                   *logrus.Logger
	deepseekService          *DeepSeekService
	intentRecognizer         *IntentRecognizer
	commandGenerator         *CommandGenerator
	hostService              HostService
	toolManager              *ToolManager
	contextManager           *ContextManager
	smartConversationManager *SmartConversationManager
	mockAIService            *MockAIService
	useMockAI                bool
}

// NewChatService 创建对话服务
func NewChatService(db *gorm.DB, cfg *config.Config, logger *logrus.Logger) ChatService {
	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建意图识别器和命令生成器
	intentRecognizer := NewIntentRecognizer(deepseekService, logger)
	commandGenerator := NewCommandGenerator(deepseekService, logger)

	// 创建智能对话管理器和模拟AI服务
	// 暂时传递nil，稍后在SetHostService中重新创建
	smartConversationManager := NewSmartConversationManager(logger, nil)
	mockAIService := NewMockAIService(logger)

	// 检查是否应该使用模拟AI服务
	useMockAI := cfg.DeepSeek.APIKey == "" || cfg.DeepSeek.APIKey == "***********************************"

	if useMockAI {
		logger.Warn("Chat Service: Using Mock AI Service - DeepSeek API key not configured or using placeholder")
	}

	service := &chatService{
		db:                       db,
		config:                   cfg,
		logger:                   logger,
		deepseekService:          deepseekService,
		intentRecognizer:         intentRecognizer,
		commandGenerator:         commandGenerator,
		contextManager:           NewContextManager(db, logger),
		smartConversationManager: smartConversationManager,
		mockAIService:            mockAIService,
		useMockAI:                useMockAI,
	}

	return service
}

// SetHostService 设置主机服务（避免循环依赖）
func (s *chatService) SetHostService(hostService HostService) {
	s.hostService = hostService
	// 创建工具管理器
	s.toolManager = NewToolManager(s.logger, hostService)
	// 重新创建智能对话管理器，传入hostService
	s.smartConversationManager = NewSmartConversationManager(s.logger, hostService)
}

// CreateSession 创建对话会话
func (s *chatService) CreateSession(userID int64, req *model.ChatSessionCreateRequest) (*model.ChatSessionResponse, error) {
	sessionID := uuid.New().String()

	session := &model.ChatSession{
		SessionID:    sessionID,
		UserID:       userID,
		Title:        req.Title,
		Status:       string(model.SessionStatusActive),
		MessageCount: 0,
		TokenUsage:   0,
	}

	// 设置默认标题
	if session.Title == "" {
		session.Title = "新对话 - " + time.Now().Format("2006-01-02 15:04")
	}

	// TODO: 设置上下文数据（如果需要的话）
	// if req.ContextData != nil {
	// 	if err := session.SetContextData(req.ContextData); err != nil {
	// 		return nil, fmt.Errorf("failed to set context data: %w", err)
	// 	}
	// }

	// 保存到数据库
	if err := s.db.Create(session).Error; err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
		"title":      session.Title,
	}).Info("Chat session created")

	return session.ToResponse(), nil
}

// CreateSessionWithID 使用指定的SessionID创建会话
func (s *chatService) CreateSessionWithID(session *model.ChatSession) error {
	// 保存到数据库
	if err := s.db.Create(session).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"user_id":    session.UserID,
		"title":      session.Title,
	}).Info("Chat session created with specified ID")

	return nil
}

// GetSessionByID 根据ID获取会话
func (s *chatService) GetSessionByID(id int64) (*model.ChatSessionResponse, error) {
	var session model.ChatSession
	// TODO: 暂时移除User预加载，后续恢复
	if err := s.db.Preload("Messages").First(&session, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return session.ToResponse(), nil
}

// GetSessionBySessionID 根据SessionID获取会话
func (s *chatService) GetSessionBySessionID(sessionID string) (*model.ChatSessionResponse, error) {
	var session model.ChatSession
	// TODO: 暂时移除User预加载，后续恢复
	if err := s.db.Preload("Messages").Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	return session.ToResponse(), nil
}

// ListSessions 获取用户的会话列表
func (s *chatService) ListSessions(userID int64, req *model.ChatSessionListQuery) (*model.ChatSessionListResponse, error) {
	query := s.db.Model(&model.ChatSession{}).Where("user_id = ?", userID)

	// 应用过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("title LIKE ?", searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count sessions: %w", err)
	}

	// 分页
	offset := (req.Page - 1) * req.Limit
	var sessions []model.ChatSession
	if err := query.Offset(offset).Limit(req.Limit).Order("last_activity DESC").Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions: %w", err)
	}

	// 转换为响应格式
	sessionResponses := make([]*model.ChatSessionResponse, len(sessions))
	for i, session := range sessions {
		sessionResponses[i] = session.ToResponse()
	}

	// 计算分页信息
	pagination := model.NewPagination(total, req.Page, req.Limit)

	return &model.ChatSessionListResponse{
		Sessions:   sessionResponses,
		Pagination: pagination,
	}, nil
}

// SendMessage 发送消息
func (s *chatService) SendMessage(req *model.ChatMessageRequest) (*model.ChatMessageResponse, error) {
	// 获取会话
	var session model.ChatSession
	if err := s.db.Where("session_id = ?", req.SessionID).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("session not found")
		}
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	// 检查会话状态
	if !session.IsActive() {
		return nil, errors.New("session is not active")
	}

	start := time.Now()

	// 创建用户消息
	userMessage := &model.ChatMessage{
		SessionID:   session.ID,
		MessageType: string(model.MessageTypeUser),
		Content:     req.Content,
	}

	// 保存用户消息
	if err := s.db.Create(userMessage).Error; err != nil {
		return nil, fmt.Errorf("failed to save user message: %w", err)
	}

	// 初始化或获取会话上下文
	sessionContext, err := s.contextManager.GetContext(req.SessionID)
	if err != nil {
		// 如果上下文不存在，创建新的
		sessionContext = &SessionContext{
			SessionID:    req.SessionID,
			UserID:       session.UserID,
			Messages:     make([]ContextMessage, 0),
			Variables:    make(map[string]interface{}),
			LastActivity: time.Now(),
			MaxMessages:  50,
		}
		s.contextManager.cache[req.SessionID] = sessionContext
	}

	// 添加用户消息到上下文
	s.contextManager.AddMessage(req.SessionID, "user", req.Content, nil)

	// 处理AI响应
	aiResponse, err := s.processAIResponse(req.SessionID, req.Content)
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"session_id": req.SessionID,
			"error":      err.Error(),
		}).Error("Failed to process AI response")

		// 创建错误响应消息
		errorMessage := &model.ChatMessage{
			SessionID:        session.ID,
			MessageType:      string(model.MessageTypeAssistant),
			Content:          "抱歉，我遇到了一些问题，请稍后再试。",
			ErrorMessage:     err.Error(),
			ProcessingTimeMs: int(time.Since(start).Milliseconds()),
		}

		if err := s.db.Create(errorMessage).Error; err != nil {
			s.logger.WithError(err).Error("Failed to save error message")
		}

		return errorMessage.ToResponse(), nil
	}

	// 创建AI响应消息
	assistantMessage := &model.ChatMessage{
		SessionID:        session.ID,
		MessageType:      string(model.MessageTypeAssistant),
		Content:          aiResponse.Content,
		AIResponse:       aiResponse.Content,
		Intent:           aiResponse.Intent,
		TokenCount:       aiResponse.TokenCount,
		ProcessingTimeMs: int(time.Since(start).Milliseconds()),
	}

	// 设置提取的参数
	if aiResponse.ExtractedParams != nil {
		if err := assistantMessage.SetExtractedParams(aiResponse.ExtractedParams); err != nil {
			s.logger.WithError(err).Warn("Failed to set extracted params")
		}
	}

	// 设置工具调用
	if aiResponse.ToolCalls != nil {
		if err := assistantMessage.SetToolCalls(aiResponse.ToolCalls); err != nil {
			s.logger.WithError(err).Warn("Failed to set tool calls")
		}
	}

	// 设置工具结果
	if aiResponse.ToolResults != nil {
		if err := assistantMessage.SetToolResults(aiResponse.ToolResults); err != nil {
			s.logger.WithError(err).Warn("Failed to set tool results")
		}
	}

	// 保存AI消息
	if err := s.db.Create(assistantMessage).Error; err != nil {
		return nil, fmt.Errorf("failed to save assistant message: %w", err)
	}

	// 添加AI响应到上下文
	metadata := map[string]interface{}{
		"intent":       aiResponse.Intent,
		"token_count":  aiResponse.TokenCount,
		"tool_calls":   aiResponse.ToolCalls,
		"tool_results": aiResponse.ToolResults,
	}
	s.contextManager.AddMessage(req.SessionID, "assistant", aiResponse.Content, metadata)

	// 保存上下文到数据库
	if err := s.contextManager.SaveContext(req.SessionID); err != nil {
		s.logger.WithError(err).Warn("Failed to save context")
	}

	// 更新会话统计
	s.updateSessionStats(session.ID, aiResponse.TokenCount)

	s.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          aiResponse.Intent,
		"processing_time": time.Since(start).Milliseconds(),
		"token_count":     aiResponse.TokenCount,
	}).Info("Message processed successfully")

	return assistantMessage.ToResponse(), nil
}

// processAIResponse 处理AI响应
func (s *chatService) processAIResponse(sessionID, userInput string) (*AIResponse, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优先使用智能对话管理器
	s.logger.WithField("session_id", sessionID).Info("Attempting to use smart conversation manager")
	smartResponse, err := s.handleWithSmartConversation(ctx, sessionID, userInput)
	if err != nil {
		s.logger.WithError(err).WithField("session_id", sessionID).Warn("Smart conversation failed, falling back to tool-based handling")
	} else {
		s.logger.WithField("session_id", sessionID).Info("Smart conversation succeeded")
		return smartResponse, nil
	}

	// 检查工具管理器是否已初始化
	if s.toolManager == nil {
		return s.handleGeneralChat(ctx, userInput)
	}

	// 使用工具调用处理用户输入
	return s.handleWithTools(ctx, sessionID, userInput)
}

// handleWithSmartConversation 使用智能对话管理器处理用户输入
func (s *chatService) handleWithSmartConversation(ctx context.Context, sessionID, userInput string) (*AIResponse, error) {
	s.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_input": userInput,
	}).Info("Starting smart conversation handling")

	// 获取会话信息
	var session model.ChatSession
	if err := s.db.Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		s.logger.WithError(err).WithField("session_id", sessionID).Error("Failed to get session")
		return nil, fmt.Errorf("failed to get session: %w", err)
	}

	s.logger.WithField("user_id", session.UserID).Info("Session found")

	// 提取意图
	intent, err := s.extractIntent(ctx, userInput)
	if err != nil {
		s.logger.WithError(err).Error("Failed to extract intent")
		return nil, fmt.Errorf("failed to extract intent: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"intent_type": intent.Type,
		"confidence":  intent.Confidence,
		"parameters":  intent.Parameters,
	}).Info("Intent extracted successfully")

	// 使用智能对话管理器处理消息
	smartResponse, err := s.smartConversationManager.ProcessMessage(ctx, sessionID, session.UserID, userInput, intent)
	if err != nil {
		return nil, fmt.Errorf("smart conversation manager failed: %w", err)
	}

	// 如果需要执行操作
	if smartResponse.Action == "execute_pending_action" && smartResponse.PendingAction != nil {
		executionResult, err := s.executePendingAction(ctx, smartResponse.PendingAction, session.UserID)
		if err != nil {
			s.logger.WithError(err).Error("Failed to execute pending action")
			smartResponse.Content = fmt.Sprintf("执行操作失败：%s", err.Error())
		} else {
			smartResponse.Content = executionResult
		}
	}

	// 转换为AIResponse格式
	return &AIResponse{
		Content:         smartResponse.Content,
		Intent:          intent.Type,
		TokenCount:      50, // 估算
		ExtractedParams: intent.Parameters,
		ToolCalls:       nil,
		ToolResults:     nil,
	}, nil
}

// extractIntent 提取用户意图
func (s *chatService) extractIntent(ctx context.Context, userInput string) (*IntentResult, error) {
	if s.useMockAI {
		// 使用模拟AI服务
		mockIntent, err := s.mockAIService.RecognizeIntent(ctx, userInput)
		if err != nil {
			return nil, err
		}

		return &IntentResult{
			Type:       mockIntent.Type,
			Confidence: mockIntent.Confidence,
			Parameters: mockIntent.Parameters,
		}, nil
	}

	// 使用真实的意图识别器
	intent, err := s.intentRecognizer.RecognizeIntent(ctx, userInput)
	if err != nil {
		// 如果失败，降级到模拟服务
		s.logger.WithError(err).Warn("Intent recognizer failed, falling back to mock AI")
		s.useMockAI = true

		mockIntent, mockErr := s.mockAIService.RecognizeIntent(ctx, userInput)
		if mockErr != nil {
			return nil, fmt.Errorf("both intent recognizer and mock AI failed: %w", err)
		}

		return &IntentResult{
			Type:       mockIntent.Type,
			Confidence: mockIntent.Confidence,
			Parameters: mockIntent.Parameters,
		}, nil
	}

	return &IntentResult{
		Type:       intent.Type,
		Confidence: intent.Confidence,
		Parameters: intent.Parameters,
	}, nil
}

// executePendingAction 执行待处理的操作
func (s *chatService) executePendingAction(ctx context.Context, action *SmartPendingAction, userID int64) (string, error) {
	switch action.Type {
	case "add_host":
		return s.executeHostAddition(ctx, action, userID)
	case "list_hosts":
		return s.executeHostList(ctx, action, userID)
	default:
		return "", fmt.Errorf("unsupported action type: %s", action.Type)
	}
}

// executeHostAddition 执行主机添加操作
func (s *chatService) executeHostAddition(ctx context.Context, action *SmartPendingAction, userID int64) (string, error) {
	// 提取参数
	ip, _ := action.Parameters["ip"].(string)
	username, _ := action.Parameters["username"].(string)
	password, _ := action.Parameters["password"].(string)

	if ip == "" || username == "" || password == "" {
		return "", fmt.Errorf("missing required parameters for host addition")
	}

	// 检查主机是否已存在
	if s.hostService != nil {
		hosts, err := s.hostService.ListHosts(&model.HostListQuery{})
		if err == nil && hosts != nil {
			for _, host := range hosts.Hosts {
				if host.IPAddress == ip {
					return fmt.Sprintf("主机 %s 已存在，无需重复添加。", ip), nil
				}
			}
		}
	}

	// 创建主机添加请求
	hostReq := &model.HostCreateRequest{
		Name:              fmt.Sprintf("host-%s", ip),
		IPAddress:         ip,
		Port:              22, // 默认SSH端口
		Username:          username,
		Password:          password,
		Description:       "通过AI对话添加",
		Environment:       "production",
		MonitoringEnabled: true,
		BackupEnabled:     false,
		CreatedBy:         userID,
	}

	// 执行主机添加
	if s.hostService != nil {
		host, err := s.hostService.CreateHost(hostReq)
		if err != nil {
			return "", fmt.Errorf("failed to create host: %w", err)
		}

		// 测试连接
		testResult, err := s.hostService.TestConnection(host.ID)
		if err != nil {
			return fmt.Sprintf("主机 %s 添加成功，但连接测试失败：%s", ip, err.Error()), nil
		}

		if testResult.Success {
			return fmt.Sprintf("✅ 主机 %s 添加成功并连接正常！", ip), nil
		} else {
			return fmt.Sprintf("主机 %s 添加成功，但连接测试失败：%s", ip, testResult.Message), nil
		}
	}

	// 如果没有主机服务，返回模拟结果
	return fmt.Sprintf("✅ 主机 %s (用户名:%s) 添加成功！", ip, username), nil
}

// executeHostList 执行主机列表查询操作
func (s *chatService) executeHostList(ctx context.Context, action *SmartPendingAction, userID int64) (string, error) {
	// 构建查询参数
	query := &model.HostListQuery{
		Page:  1,
		Limit: 50, // 默认显示前50台主机
	}

	// 检查是否有过滤条件
	if filter, ok := action.Parameters["filter"].(string); ok {
		switch filter {
		case "status":
			// 如果需要状态过滤，可以在这里添加
		case "account":
			// 账号信息过滤
		case "info":
			// 信息过滤
		}
	}

	// 调用主机服务获取主机列表
	hostListResp, err := s.hostService.ListHosts(query)
	if err != nil {
		s.logger.WithError(err).WithField("user_id", userID).Error("Failed to list hosts")
		return "获取主机列表失败，请稍后重试或联系管理员", nil
	}

	// 构建响应内容
	if len(hostListResp.Hosts) == 0 {
		return "📋 当前没有已添加的主机\n\n💡 您可以通过以下方式添加主机：\n• 输入：添加主机 IP地址 用户名 密码\n• 例如：添加主机 ************* root mypassword", nil
	}

	// 格式化主机列表
	var content strings.Builder
	content.WriteString("📋 主机列表：\n\n")

	onlineCount := 0
	offlineCount := 0

	for i, host := range hostListResp.Hosts {
		statusIcon := "🔴"
		switch host.Status {
		case "online":
			statusIcon = "🟢"
			onlineCount++
		case "offline":
			statusIcon = "🔴"
			offlineCount++
		case "unknown":
			statusIcon = "🟡"
		case "error":
			statusIcon = "❌"
		case "maintenance":
			statusIcon = "🔧"
		}

		content.WriteString(fmt.Sprintf("%d. %s **%s** (%s)\n", i+1, statusIcon, host.Name, host.IPAddress))
		content.WriteString(fmt.Sprintf("   • 用户名：%s\n", host.Username))
		content.WriteString(fmt.Sprintf("   • 端口：%d\n", host.Port))
		content.WriteString(fmt.Sprintf("   • 环境：%s\n", host.Environment))
		if host.Description != "" {
			content.WriteString(fmt.Sprintf("   • 描述：%s\n", host.Description))
		}
		content.WriteString("\n")
	}

	content.WriteString(fmt.Sprintf("📊 统计信息：\n"))
	content.WriteString(fmt.Sprintf("• 总计：%d 台主机\n", len(hostListResp.Hosts)))
	content.WriteString(fmt.Sprintf("• 在线：%d 台\n", onlineCount))
	content.WriteString(fmt.Sprintf("• 离线：%d 台\n", offlineCount))

	s.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"host_count": len(hostListResp.Hosts),
		"online":     onlineCount,
		"offline":    offlineCount,
	}).Info("Host list retrieved via chat service")

	return content.String(), nil
}

// handleWithTools 使用工具处理用户输入
func (s *chatService) handleWithTools(ctx context.Context, sessionID, userInput string) (*AIResponse, error) {
	// 构建系统提示
	systemPrompt := `你是一个专业的AI运维助手，专门通过对话式交互帮助用户管理IT基础设施。

## 核心能力
1. **主机管理**: 查看、添加、配置主机，检查连接状态
2. **监控告警**: 实时监控系统状态，管理告警规则
3. **性能分析**: 分析CPU、内存、磁盘等资源使用情况
4. **日志分析**: 查看和分析系统日志
5. **统计报表**: 生成各类运维统计报表
6. **安全操作**: 执行安全的运维命令和操作

## 交互原则
- **对话式引导**: 通过自然对话引导用户完成复杂操作
- **分步骤执行**: 将复杂任务分解为简单的对话步骤
- **智能建议**: 主动提供最佳实践建议和优化方案
- **风险提醒**: 对于可能有风险的操作，详细说明并征求确认
- **结果解读**: 不仅提供数据，还要解读数据的含义和建议

## 响应格式
- 使用清晰、友好的语言
- 提供具体的操作指导
- 包含相关的技术细节
- 给出后续建议和最佳实践

当用户询问运维相关问题时，请：
1. 理解用户意图
2. 调用相应工具获取信息
3. 提供清晰的解释和建议
4. 引导用户进行下一步操作`

	// 获取可用工具
	tools := s.toolManager.GetAvailableTools()

	// 构建消息（包含历史上下文）
	messages := []Message{
		{Role: "system", Content: systemPrompt},
	}

	// 添加历史上下文消息
	if recentMessages, err := s.contextManager.GetRecentMessages(sessionID, 10); err == nil {
		messages = append(messages, recentMessages...)
	}

	// 添加当前用户输入
	messages = append(messages, Message{Role: "user", Content: userInput})

	// 发送带工具的请求
	response, err := s.deepseekService.ChatWithTools(ctx, messages, tools)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	choice := response.Choices[0]
	aiResponse := &AIResponse{
		Content:    choice.Message.Content,
		Intent:     "tool_calling",
		TokenCount: response.Usage.TotalTokens,
	}

	// 处理工具调用
	if len(choice.Message.ToolCalls) > 0 {
		toolResults := make([]map[string]interface{}, 0, len(choice.Message.ToolCalls))

		for _, toolCall := range choice.Message.ToolCalls {
			result, err := s.toolManager.ExecuteTool(ctx, toolCall)
			if err != nil {
				s.logger.WithError(err).WithField("tool", toolCall.Function.Name).Error("Tool execution failed")
				toolResults = append(toolResults, map[string]interface{}{
					"tool_call_id": toolCall.ID,
					"error":        err.Error(),
				})
				continue
			}

			toolResults = append(toolResults, map[string]interface{}{
				"tool_call_id": toolCall.ID,
				"result":       result,
			})
		}

		// 将工具结果发送回AI获取最终响应
		if len(toolResults) > 0 {
			finalResponse, err := s.getToolResultResponse(ctx, messages, choice.Message.ToolCalls, toolResults)
			if err != nil {
				s.logger.WithError(err).Warn("Failed to get tool result response")
			} else {
				aiResponse.Content = finalResponse
			}
		}

		aiResponse.ToolCalls = make([]map[string]interface{}, len(choice.Message.ToolCalls))
		for i, toolCall := range choice.Message.ToolCalls {
			aiResponse.ToolCalls[i] = map[string]interface{}{
				"id":       toolCall.ID,
				"type":     toolCall.Type,
				"function": toolCall.Function,
			}
		}
		aiResponse.ToolResults = toolResults
	}

	return aiResponse, nil
}

// getToolResultResponse 获取工具结果的最终响应
func (s *chatService) getToolResultResponse(ctx context.Context, originalMessages []Message, toolCalls []ToolCall, toolResults []map[string]interface{}) (string, error) {
	// 构建包含工具调用和结果的消息序列
	messages := make([]Message, len(originalMessages))
	copy(messages, originalMessages)

	// 添加助手的工具调用消息
	assistantMessage := Message{
		Role:      "assistant",
		Content:   "",
		ToolCalls: toolCalls,
	}
	messages = append(messages, assistantMessage)

	// 添加工具结果消息
	for _, result := range toolResults {
		toolCallID, _ := result["tool_call_id"].(string)
		resultData, _ := result["result"]
		errorMsg, _ := result["error"].(string)

		var content string
		if errorMsg != "" {
			content = fmt.Sprintf("工具执行失败: %s", errorMsg)
		} else {
			resultBytes, _ := json.Marshal(resultData)
			content = string(resultBytes)
		}

		toolMessage := Message{
			Role:       "tool",
			Content:    content,
			ToolCallID: toolCallID,
		}
		messages = append(messages, toolMessage)
	}

	// 发送请求获取最终响应
	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return "", err
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no response from AI")
	}

	return response.Choices[0].Message.Content, nil
}

// handleCommandExecution 处理命令执行
func (s *chatService) handleCommandExecution(ctx context.Context, intent *Intent, userInput string) (*AIResponse, error) {
	// 生成命令
	command, err := s.commandGenerator.GenerateCommand(ctx, intent, userInput)
	if err != nil {
		return nil, fmt.Errorf("failed to generate command: %w", err)
	}

	// 检查命令安全性
	if !s.isCommandSafe(command) {
		return &AIResponse{
			Content:         "抱歉，出于安全考虑，我不能执行这个命令。",
			Intent:          intent.Type,
			ExtractedParams: intent.Parameters,
		}, nil
	}

	// 如果指定了主机，执行命令
	if hostParam, ok := intent.Parameters["host"]; ok {
		if hostStr, ok := hostParam.(string); ok {
			// TODO: 实现在指定主机上执行命令
			_ = hostStr
			return &AIResponse{
				Content:    "命令执行功能正在开发中",
				TokenCount: 10,
			}, nil
		}
	}

	// 返回生成的命令供用户确认
	response := fmt.Sprintf("我为您生成了以下命令：\n\n```bash\n%s\n```\n\n请确认是否要执行此命令？", command)

	return &AIResponse{
		Content:         response,
		Intent:          intent.Type,
		ExtractedParams: intent.Parameters,
		ToolCalls: []map[string]interface{}{
			{
				"type":    "command_generation",
				"command": command,
			},
		},
	}, nil
}

// handleGeneralChat 处理一般对话
func (s *chatService) handleGeneralChat(ctx context.Context, userInput string) (*AIResponse, error) {
	systemPrompt := `你是一个专业的AI运维助手，专门通过对话式交互帮助用户管理IT基础设施。

## 核心能力
1. **主机管理**: 指导用户添加、配置、管理服务器主机
2. **监控告警**: 帮助用户理解和处理系统告警
3. **性能分析**: 解读系统性能数据，提供优化建议
4. **故障排查**: 协助用户诊断和解决系统问题
5. **最佳实践**: 提供运维最佳实践和安全建议

## 交互风格
- 使用清晰、友好的对话语言
- 提供具体的操作步骤和指导
- 主动询问用户需求的细节
- 解释技术概念，让用户容易理解
- 在执行重要操作前征求确认

## 响应原则
- 如果用户询问主机相关问题，详细说明主机管理的步骤
- 如果用户询问告警问题，解释告警的含义和处理方法
- 如果用户询问监控问题，说明监控指标的意义
- 如果用户询问统计报表，解释数据的含义和趋势
- 始终提供后续建议和最佳实践

请用专业但易懂的语言回答用户问题，并主动引导用户进行下一步操作。`

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userInput},
	}

	response, err := s.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI response: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	return &AIResponse{
		Content:    response.Choices[0].Message.Content,
		Intent:     "general_chat",
		TokenCount: response.Usage.TotalTokens,
	}, nil
}

// isCommandSafe 检查命令安全性
func (s *chatService) isCommandSafe(command string) bool {
	// 危险命令列表
	dangerousCommands := []string{
		"rm -rf", "dd if=", "mkfs", "fdisk", "parted",
		"shutdown", "reboot", "halt", "poweroff",
		"passwd", "userdel", "groupdel",
		"iptables -F", "ufw --force",
		"chmod 777", "chown -R",
	}

	commandLower := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		if strings.Contains(commandLower, dangerous) {
			return false
		}
	}

	return true
}

// updateSessionStats 更新会话统计
func (s *chatService) updateSessionStats(sessionID int64, tokenCount int) {
	updates := map[string]interface{}{
		"message_count": gorm.Expr("message_count + 1"),
		"token_usage":   gorm.Expr("token_usage + ?", tokenCount),
		"last_activity": time.Now(),
	}

	if err := s.db.Model(&model.ChatSession{}).Where("id = ?", sessionID).Updates(updates).Error; err != nil {
		s.logger.WithFields(logrus.Fields{
			"session_id": sessionID,
			"error":      err.Error(),
		}).Error("Failed to update session stats")
	}
}

// sessionToResponse 和 messageToResponse 方法已废弃
// 直接使用 model.ToResponse() 方法

// AIResponse AI响应
type AIResponse struct {
	Content         string                   `json:"content"`
	Intent          string                   `json:"intent"`
	ExtractedParams map[string]interface{}   `json:"extracted_params,omitempty"`
	ToolCalls       []map[string]interface{} `json:"tool_calls,omitempty"`
	ToolResults     []map[string]interface{} `json:"tool_results,omitempty"`
	TokenCount      int                      `json:"token_count"`
}

// 请求响应结构体定义
type CreateSessionRequest struct {
	Title       string                 `json:"title"`
	ContextData map[string]interface{} `json:"context_data"`
}

type ListSessionsRequest struct {
	Page   int    `json:"page"`
	Limit  int    `json:"limit"`
	Status string `json:"status"`
	Search string `json:"search"`
}

type SendMessageRequest struct {
	SessionID string `json:"session_id" binding:"required"`
	Content   string `json:"content" binding:"required"`
	Stream    bool   `json:"stream"`
}

type SessionResponse struct {
	ID           int64                  `json:"id"`
	SessionID    string                 `json:"session_id"`
	UserID       int64                  `json:"user_id"`
	Title        string                 `json:"title"`
	Status       string                 `json:"status"`
	MessageCount int                    `json:"message_count"`
	TokenUsage   int                    `json:"token_usage"`
	StartedAt    time.Time              `json:"started_at"`
	LastActivity time.Time              `json:"last_activity"`
	Messages     []*MessageResponse     `json:"messages,omitempty"`
	ContextData  map[string]interface{} `json:"context_data"`
}

type MessageResponse struct {
	ID               int64                    `json:"id"`
	MessageType      string                   `json:"message_type"`
	Content          string                   `json:"content"`
	Intent           string                   `json:"intent,omitempty"`
	ExtractedParams  map[string]interface{}   `json:"extracted_params,omitempty"`
	ToolCalls        []map[string]interface{} `json:"tool_calls,omitempty"`
	ToolResults      []map[string]interface{} `json:"tool_results,omitempty"`
	TokenCount       int                      `json:"token_count"`
	ProcessingTimeMs int                      `json:"processing_time_ms"`
	CreatedAt        time.Time                `json:"created_at"`
	ErrorMessage     string                   `json:"error_message,omitempty"`
}

type ListSessionsResponse struct {
	Sessions   []*SessionResponse `json:"sessions"`
	Pagination *Pagination        `json:"pagination"`
}

type GetMessagesRequest struct {
	Page  int `json:"page"`
	Limit int `json:"limit"`
}

type GetMessagesResponse struct {
	Messages   []*MessageResponse `json:"messages"`
	Pagination *Pagination        `json:"pagination"`
}

// DeleteSession 删除会话
func (s *chatService) DeleteSession(sessionID string) error {
	// 删除会话及其相关消息
	if err := s.db.Where("session_id = ?", sessionID).Delete(&model.ChatSession{}).Error; err != nil {
		return fmt.Errorf("failed to delete session: %w", err)
	}

	s.logger.WithField("session_id", sessionID).Info("Chat session deleted")
	return nil
}

// EndSession 结束会话
func (s *chatService) EndSession(sessionID string) error {
	if err := s.db.Model(&model.ChatSession{}).
		Where("session_id = ?", sessionID).
		Update("status", string(model.SessionStatusEnded)).Error; err != nil {
		return fmt.Errorf("failed to end session: %w", err)
	}

	s.logger.WithField("session_id", sessionID).Info("Chat session ended")
	return nil
}

// GetMessages 获取会话消息
func (s *chatService) GetMessages(sessionID string, req *model.ChatSessionListQuery) (*model.ChatSessionListResponse, error) {
	// 简化实现，返回空响应
	return &model.ChatSessionListResponse{
		Sessions:   []*model.ChatSessionResponse{},
		Pagination: model.NewPagination(0, 1, 10),
	}, nil
}
