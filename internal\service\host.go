package service

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"strings"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
	"gorm.io/gorm"
)

// hostService 主机服务实现
type hostService struct {
	db        *gorm.DB
	config    *config.Config
	logger    *logrus.Logger
	cipher    cipher.AEAD
	sshPool   *SSHConnectionPool
	wsManager *WebSocketManager
}

// NewHostService 创建主机服务
func NewHostService(db *gorm.DB, cfg *config.Config, logger *logrus.Logger) HostService {
	// 初始化加密器
	block, err := aes.NewCipher([]byte(cfg.Security.EncryptionKey))
	if err != nil {
		logger.Fatalf("Failed to create cipher: %v", err)
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		logger.Fatalf("Failed to create AEAD: %v", err)
	}

	service := &hostService{
		db:     db,
		config: cfg,
		logger: logger,
		cipher: aead,
	}

	// 创建SSH连接池（传入service自身用于密码解密）
	sshPool := NewSSHConnectionPool(logger, nil, service)
	service.sshPool = sshPool

	// 启动SSH连接池维护例程
	go sshPool.StartMaintenanceRoutine(context.Background())

	return service
}

// SetWebSocketManager 设置WebSocket管理器
func (s *hostService) SetWebSocketManager(wsManager *WebSocketManager) {
	s.wsManager = wsManager
}

// CreateHost 创建主机
func (s *hostService) CreateHost(req *model.HostCreateRequest) (*model.HostResponse, error) {
	// 验证认证方式：至少需要密码或SSH密钥路径之一
	if req.Password == "" && req.SSHKeyPath == "" {
		return nil, errors.New("至少需要提供密码或SSH密钥路径中的一种认证方式")
	}

	// 检查主机名是否已存在
	var existingHost model.Host
	if err := s.db.Where("name = ?", req.Name).First(&existingHost).Error; err == nil {
		return nil, errors.New("主机名已存在，请使用不同的主机名")
	}

	// 检查IP是否已存在
	if err := s.db.Where("ip_address = ?", req.IPAddress).First(&existingHost).Error; err == nil {
		return nil, errors.New("IP地址已存在，请使用不同的IP地址")
	}

	// 创建主机模型
	host := &model.Host{
		Name:              req.Name,
		IPAddress:         req.IPAddress,
		Port:              req.Port,
		Username:          req.Username,
		Description:       req.Description,
		Environment:       req.Environment,
		GroupName:         req.GroupName,
		MonitoringEnabled: req.MonitoringEnabled,
		BackupEnabled:     req.BackupEnabled,
		Status:            "unknown",
		CreatedBy:         req.CreatedBy,
	}

	// 设置默认端口
	if host.Port == 0 {
		host.Port = 22
	}

	// 设置默认环境
	if host.Environment == "" {
		host.Environment = "production"
	}

	// 加密密码
	if req.Password != "" {
		encryptedPassword, err := s.encryptData(req.Password)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt password: %w", err)
		}
		host.PasswordEncrypted = encryptedPassword
	}

	// 加密SSH密钥密码
	if req.SSHKeyPassphrase != "" {
		encryptedPassphrase, err := s.encryptData(req.SSHKeyPassphrase)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt SSH key passphrase: %w", err)
		}
		host.SSHKeyPassphraseEncrypted = encryptedPassphrase
	}

	// 设置SSH密钥路径
	if req.SSHKeyPath != "" {
		host.SSHKeyPath = req.SSHKeyPath
	}

	// 设置标签
	if err := host.SetTags(req.Tags); err != nil {
		return nil, fmt.Errorf("failed to set tags: %w", err)
	}

	// 保存到数据库
	if err := s.db.Create(host).Error; err != nil {
		return nil, fmt.Errorf("failed to create host: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"host_id":    host.ID,
		"host_name":  host.Name,
		"ip_address": host.IPAddress,
		"created_by": host.CreatedBy,
	}).Info("Host created successfully")

	// 异步测试连接
	go s.testConnectionAsync(host.ID)

	return host.ToResponse(), nil
}

// GetHostByID 根据ID获取主机
func (s *hostService) GetHostByID(id int64) (*model.HostResponse, error) {
	var host model.Host
	if err := s.db.Preload("Creator").First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	return host.ToResponse(), nil
}

// GetHostByName 根据名称获取主机
func (s *hostService) GetHostByName(name string) (*model.HostResponse, error) {
	var host model.Host
	if err := s.db.Preload("Creator").Where("name = ?", name).First(&host).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	return host.ToResponse(), nil
}

// UpdateHost 更新主机
func (s *hostService) UpdateHost(id int64, req *model.HostUpdateRequest) (*model.HostResponse, error) {
	var host model.Host
	if err := s.db.First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	// 更新字段
	if req.Name != "" && req.Name != host.Name {
		// 检查名称是否已被其他主机使用
		var existingHost model.Host
		if err := s.db.Where("name = ? AND id != ?", req.Name, id).First(&existingHost).Error; err == nil {
			return nil, errors.New("host name already exists")
		}
		host.Name = req.Name
	}

	if req.IPAddress != "" && req.IPAddress != host.IPAddress {
		// 检查IP是否已被其他主机使用
		var existingHost model.Host
		if err := s.db.Where("ip_address = ? AND id != ?", req.IPAddress, id).First(&existingHost).Error; err == nil {
			return nil, errors.New("host IP address already exists")
		}
		host.IPAddress = req.IPAddress
	}

	if req.Port != 0 {
		host.Port = req.Port
	}

	if req.Username != "" {
		host.Username = req.Username
	}

	if req.Description != "" {
		host.Description = req.Description
	}

	if req.Environment != "" {
		host.Environment = req.Environment
	}

	if req.GroupName != "" {
		host.GroupName = req.GroupName
	}

	if req.MonitoringEnabled != nil {
		host.MonitoringEnabled = *req.MonitoringEnabled
	}

	if req.BackupEnabled != nil {
		host.BackupEnabled = *req.BackupEnabled
	}

	// 更新密码
	if req.Password != "" {
		encryptedPassword, err := s.encryptData(req.Password)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt password: %w", err)
		}
		host.PasswordEncrypted = encryptedPassword
	}

	// 更新SSH密钥信息
	if req.SSHKeyPath != "" {
		host.SSHKeyPath = req.SSHKeyPath
	}

	if req.SSHKeyPassphrase != "" {
		encryptedPassphrase, err := s.encryptData(req.SSHKeyPassphrase)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt SSH key passphrase: %w", err)
		}
		host.SSHKeyPassphraseEncrypted = encryptedPassphrase
	}

	// 更新标签
	if req.Tags != nil {
		if err := host.SetTags(req.Tags); err != nil {
			return nil, fmt.Errorf("failed to set tags: %w", err)
		}
	}

	// 保存更新
	if err := s.db.Save(&host).Error; err != nil {
		return nil, fmt.Errorf("failed to update host: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"host_id":   host.ID,
		"host_name": host.Name,
	}).Info("Host updated successfully")

	return host.ToResponse(), nil
}

// DeleteHost 删除主机
func (s *hostService) DeleteHost(id int64) error {
	var host model.Host
	if err := s.db.First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("host not found")
		}
		return fmt.Errorf("failed to get host: %w", err)
	}

	// 软删除
	if err := s.db.Delete(&host).Error; err != nil {
		return fmt.Errorf("failed to delete host: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"host_id":   host.ID,
		"host_name": host.Name,
	}).Info("Host deleted successfully")

	return nil
}

// ListHosts 获取主机列表
func (s *hostService) ListHosts(req *model.HostListQuery) (*model.HostListResponse, error) {
	query := s.db.Model(&model.Host{}).Preload("Creator")

	// 应用过滤条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.Environment != "" {
		query = query.Where("environment = ?", req.Environment)
	}

	if req.GroupName != "" {
		query = query.Where("group_name = ?", req.GroupName)
	}

	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("name LIKE ? OR ip_address LIKE ? OR description LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	if req.Tags != "" {
		tags := strings.Split(req.Tags, ",")
		for _, tag := range tags {
			tag = strings.TrimSpace(tag)
			if tag != "" {
				query = query.Where("tags LIKE ?", "%\""+tag+"\"%")
			}
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count hosts: %w", err)
	}

	// 分页
	offset := (req.Page - 1) * req.Limit
	var hosts []model.Host
	if err := query.Offset(offset).Limit(req.Limit).Order("created_at DESC").Find(&hosts).Error; err != nil {
		return nil, fmt.Errorf("failed to get hosts: %w", err)
	}

	// 转换为响应格式
	hostResponses := make([]*model.HostResponse, len(hosts))
	for i, host := range hosts {
		hostResponses[i] = host.ToResponse()
	}

	// 计算分页信息
	pagination := model.NewPagination(total, req.Page, req.Limit)

	return &model.HostListResponse{
		Hosts:      hostResponses,
		Pagination: pagination,
	}, nil
}

// TestConnection 测试主机连接
func (s *hostService) TestConnection(id int64) (*model.HostTestResponse, error) {
	// 获取主机信息
	var host model.Host
	if err := s.db.First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	// 使用SSH连接池测试连接
	result, err := s.sshPool.TestConnection(&host)
	if err != nil {
		return &model.HostTestResponse{
			Success:  false,
			Message:  err.Error(),
			Duration: 0,
			TestedAt: time.Now(),
		}, nil
	}

	// 更新主机状态
	if result.Success {
		s.updateHostStatus(id, "online")
	} else {
		s.updateHostStatus(id, "offline")
	}

	// 转换结果格式
	response := &model.HostTestResponse{
		Success:  result.Success,
		Message:  result.Message,
		Duration: int(result.Duration.Milliseconds()),
		TestedAt: time.Now(),
	}

	// 如果连接成功，获取系统信息
	if result.Success && result.Output != "" {
		response.OSInfo = strings.TrimSpace(result.Output)
		response.SystemInfo = map[string]interface{}{
			"os_info": response.OSInfo,
		}
	}

	return response, nil
}

// 加密数据
func (s *hostService) encryptData(data string) (string, error) {
	nonce := make([]byte, s.cipher.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := s.cipher.Seal(nonce, nonce, []byte(data), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// 解密数据
func (s *hostService) decryptData(encryptedData string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	nonceSize := s.cipher.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := s.cipher.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// 创建SSH客户端
func (s *hostService) createSSHClient(host *model.Host) (*ssh.Client, error) {
	config := &ssh.ClientConfig{
		User:            host.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 生产环境应该验证主机密钥
		Timeout:         s.config.SSH.Timeout,
	}

	// 配置认证方式
	if host.PasswordEncrypted != "" {
		password, err := s.decryptData(host.PasswordEncrypted)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt password: %w", err)
		}
		config.Auth = append(config.Auth, ssh.Password(password))
	}

	// TODO: 添加SSH密钥认证支持

	// 连接SSH
	address := fmt.Sprintf("%s:%d", host.IPAddress, host.Port)
	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}

	return client, nil
}

// 异步测试连接
func (s *hostService) testConnectionAsync(hostID int64) {
	_, err := s.TestConnection(hostID)
	if err != nil {
		s.logger.WithFields(logrus.Fields{
			"host_id": hostID,
			"error":   err.Error(),
		}).Warn("Async connection test failed")
		s.updateHostStatus(hostID, "error")
	}
}

// 更新主机状态
func (s *hostService) updateHostStatus(hostID int64, status string) {
	if err := s.db.Model(&model.Host{}).Where("id = ?", hostID).Update("status", status).Error; err != nil {
		s.logger.WithFields(logrus.Fields{
			"host_id": hostID,
			"status":  status,
			"error":   err.Error(),
		}).Error("Failed to update host status")
	}
}

// ExecuteCommand 执行远程命令
func (s *hostService) ExecuteCommand(id int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error) {
	return s.ExecuteCommandWithProgress(id, req, "")
}

// ExecuteCommandWithProgress 执行远程命令并发送进度更新
func (s *hostService) ExecuteCommandWithProgress(id int64, req *model.CommandExecuteRequest, sessionID string) (*model.CommandExecuteResponse, error) {
	// 获取主机信息
	var host model.Host
	if err := s.db.First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	start := time.Now()

	// 发送开始执行的进度更新
	if s.wsManager != nil && sessionID != "" {
		s.sendCommandProgress(sessionID, &CommandProgressMessage{
			Command:     req.Command,
			HostID:      id,
			Status:      "starting",
			Progress:    0,
			StartTime:   start,
			ElapsedTime: "0s",
		})
	}

	// 设置工作目录
	command := req.Command
	if req.WorkingDirectory != "" {
		command = fmt.Sprintf("cd %s && %s", req.WorkingDirectory, command)
	}

	// 设置超时
	timeout := time.Duration(req.Timeout) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second
	}

	// 发送执行中的进度更新
	if s.wsManager != nil && sessionID != "" {
		s.sendCommandProgress(sessionID, &CommandProgressMessage{
			Command:     req.Command,
			HostID:      id,
			Status:      "executing",
			Progress:    50,
			StartTime:   start,
			ElapsedTime: time.Since(start).String(),
		})
	}

	// 使用SSH连接池执行命令
	result, err := s.sshPool.ExecuteCommand(&host, command, timeout)

	// 发送完成的进度更新
	if s.wsManager != nil && sessionID != "" {
		status := "completed"
		progress := 100
		if err != nil {
			status = "failed"
			progress = 0
		}

		s.sendCommandProgress(sessionID, &CommandProgressMessage{
			Command:     req.Command,
			HostID:      id,
			Status:      status,
			Progress:    progress,
			StartTime:   start,
			ElapsedTime: time.Since(start).String(),
			Error: func() string {
				if err != nil {
					return err.Error()
				} else {
					return ""
				}
			}(),
		})
	}

	if err != nil {
		return &model.CommandExecuteResponse{
			Command:      req.Command,
			ExitCode:     -1,
			ErrorMessage: err.Error(),
			Duration:     int(time.Since(start).Milliseconds()),
			ExecutedAt:   time.Now(),
		}, nil
	}

	// 解析结果
	stdout := result.Output
	stderr := result.Error
	exitCode := result.ExitCode
	duration := int(result.Duration.Milliseconds())

	// 构建响应
	response := &model.CommandExecuteResponse{
		Command:    req.Command,
		ExitCode:   exitCode,
		Stdout:     stdout,
		Stderr:     stderr,
		Duration:   duration,
		ExecutedAt: result.Timestamp,
	}

	// 更新连接计数
	s.db.Model(&model.Host{}).Where("id = ?", id).Updates(map[string]interface{}{
		"connection_count": gorm.Expr("connection_count + 1"),
		"last_connected":   time.Now(),
	})

	s.logger.WithFields(logrus.Fields{
		"host_id":   id,
		"command":   req.Command,
		"exit_code": exitCode,
		"duration":  duration,
	}).Info("Command executed")

	return response, nil
}

// GetHostStatus 获取主机状态
func (s *hostService) GetHostStatus(id int64) (*model.HostResponse, error) {
	var host model.Host
	if err := s.db.First(&host, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("host not found")
		}
		return nil, fmt.Errorf("failed to get host: %w", err)
	}

	// 获取系统信息
	systemInfo := make(map[string]interface{})

	if host.IsOnline() {
		// 执行系统信息收集命令
		commands := map[string]string{
			"uptime":    "uptime",
			"load":      "cat /proc/loadavg",
			"memory":    "free -m",
			"disk":      "df -h",
			"processes": "ps aux | wc -l",
		}

		for key, cmd := range commands {
			if resp, err := s.ExecuteCommand(id, &model.CommandExecuteRequest{
				Command: cmd,
				Timeout: 10,
			}); err == nil && resp.ExitCode == 0 {
				systemInfo[key] = strings.TrimSpace(resp.Stdout)
			}
		}
	}

	return host.ToResponse(), nil
}

// UpdateHostStatus 更新主机状态
func (s *hostService) UpdateHostStatus(id int64, status string) error {
	if !model.IsValidHostStatus(status) {
		return errors.New("invalid host status")
	}

	if err := s.db.Model(&model.Host{}).Where("id = ?", id).Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update host status: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"host_id": id,
		"status":  status,
	}).Info("Host status updated")

	return nil
}

// hostToResponse 转换主机模型为响应格式 (已废弃，直接使用host.ToResponse())
// func (s *hostService) hostToResponse(host *model.Host) *model.HostResponse {
// 	return host.ToResponse()
// }

// 请求响应结构体定义
type CreateHostRequest struct {
	Name              string   `json:"name" binding:"required"`
	IPAddress         string   `json:"ip_address" binding:"required,ip"`
	Port              int      `json:"port"`
	Username          string   `json:"username" binding:"required"`
	Password          string   `json:"password"`
	SSHKeyPath        string   `json:"ssh_key_path"`
	SSHKeyPassphrase  string   `json:"ssh_key_passphrase"`
	Description       string   `json:"description"`
	Environment       string   `json:"environment"`
	GroupName         string   `json:"group_name"`
	Tags              []string `json:"tags"`
	MonitoringEnabled bool     `json:"monitoring_enabled"`
	BackupEnabled     bool     `json:"backup_enabled"`
	CreatedBy         int64    `json:"-"`
}

type UpdateHostRequest struct {
	Name              string   `json:"name"`
	IPAddress         string   `json:"ip_address" binding:"omitempty,ip"`
	Port              int      `json:"port"`
	Username          string   `json:"username"`
	Password          string   `json:"password"`
	SSHKeyPath        string   `json:"ssh_key_path"`
	SSHKeyPassphrase  string   `json:"ssh_key_passphrase"`
	Description       string   `json:"description"`
	Environment       string   `json:"environment"`
	GroupName         string   `json:"group_name"`
	Tags              []string `json:"tags"`
	MonitoringEnabled *bool    `json:"monitoring_enabled"`
	BackupEnabled     *bool    `json:"backup_enabled"`
}

type ListHostsRequest struct {
	Page        int    `json:"page"`
	Limit       int    `json:"limit"`
	Status      string `json:"status"`
	Environment string `json:"environment"`
	GroupName   string `json:"group_name"`
	Search      string `json:"search"`
	Tags        string `json:"tags"`
}

type HostResponse struct {
	ID                int64      `json:"id"`
	Name              string     `json:"name"`
	IPAddress         string     `json:"ip_address"`
	Port              int        `json:"port"`
	Username          string     `json:"username"`
	Description       string     `json:"description"`
	Status            string     `json:"status"`
	OSType            string     `json:"os_type"`
	OSVersion         string     `json:"os_version"`
	Tags              []string   `json:"tags"`
	Environment       string     `json:"environment"`
	GroupName         string     `json:"group_name"`
	MonitoringEnabled bool       `json:"monitoring_enabled"`
	BackupEnabled     bool       `json:"backup_enabled"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	LastConnected     *time.Time `json:"last_connected"`
	ConnectionCount   int        `json:"connection_count"`
}

type ListHostsResponse struct {
	Hosts      []*HostResponse `json:"hosts"`
	Pagination *Pagination     `json:"pagination"`
}

type ExecuteCommandRequest struct {
	Command          string `json:"command" binding:"required"`
	Timeout          int    `json:"timeout"`
	WorkingDirectory string `json:"working_directory"`
}

type ExecuteCommandResponse struct {
	Command      string    `json:"command"`
	ExitCode     int       `json:"exit_code"`
	Stdout       string    `json:"stdout"`
	Stderr       string    `json:"stderr"`
	Duration     int       `json:"duration"`
	ExecutedAt   time.Time `json:"executed_at"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

type TestConnectionResponse struct {
	Success    bool                   `json:"success"`
	Message    string                 `json:"message"`
	Duration   int                    `json:"duration"`
	TestedAt   time.Time              `json:"tested_at"`
	OSInfo     string                 `json:"os_info,omitempty"`
	SystemInfo map[string]interface{} `json:"system_info,omitempty"`
}

type HostStatusResponse struct {
	ID            int64                  `json:"id"`
	Name          string                 `json:"name"`
	Status        string                 `json:"status"`
	LastConnected *time.Time             `json:"last_connected"`
	SystemInfo    map[string]interface{} `json:"system_info"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// sendCommandProgress 发送命令执行进度
func (s *hostService) sendCommandProgress(sessionID string, progress *CommandProgressMessage) {
	if s.wsManager != nil {
		err := s.wsManager.SendCommandProgress(sessionID, progress)
		if err != nil {
			s.logger.WithError(err).WithField("session_id", sessionID).
				Warn("Failed to send command progress")
		}
	}
}
