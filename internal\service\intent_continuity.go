package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// IntentContinuityManager 意图连续性管理器
type IntentContinuityManager struct {
	logger           *logrus.Logger
	intentRecognizer *IntentRecognizer
	contextManager   *EnhancedContextManager
}

// IntentContinuity 意图连续性
type IntentContinuity struct {
	SessionID        string                 `json:"session_id"`
	CurrentIntent    string                 `json:"current_intent"`
	IntentChain      []IntentChainNode      `json:"intent_chain"`
	PendingActions   []PendingAction        `json:"pending_actions"`
	ContextVariables map[string]interface{} `json:"context_variables"`
	LastUpdate       time.Time              `json:"last_update"`
}

// IntentChainNode 意图链节点
type IntentChainNode struct {
	Intent       string                 `json:"intent"`
	Confidence   float64                `json:"confidence"`
	Parameters   map[string]interface{} `json:"parameters"`
	Status       string                 `json:"status"` // pending, in_progress, completed, failed
	StartTime    time.Time              `json:"start_time"`
	EndTime      *time.Time             `json:"end_time,omitempty"`
	Dependencies []string               `json:"dependencies,omitempty"`
	Results      map[string]interface{} `json:"results,omitempty"`
}

// PendingAction 待处理动作
type PendingAction struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Priority    int                    `json:"priority"`
	CreatedAt   time.Time              `json:"created_at"`
	DueAt       *time.Time             `json:"due_at,omitempty"`
	Retries     int                    `json:"retries"`
	MaxRetries  int                    `json:"max_retries"`
}

// NewIntentContinuityManager 创建意图连续性管理器
func NewIntentContinuityManager(logger *logrus.Logger, intentRecognizer *IntentRecognizer, contextManager *EnhancedContextManager) *IntentContinuityManager {
	return &IntentContinuityManager{
		logger:           logger,
		intentRecognizer: intentRecognizer,
		contextManager:   contextManager,
	}
}

// ProcessIntentContinuity 处理意图连续性
func (icm *IntentContinuityManager) ProcessIntentContinuity(ctx context.Context, sessionID, userMessage string) (*IntentContinuityResult, error) {
	// 获取增强上下文
	enhancedCtx, err := icm.contextManager.GetEnhancedContext(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enhanced context: %w", err)
	}

	// 识别当前意图
	currentIntentResult, err := icm.intentRecognizer.RecognizeIntent(ctx, userMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to recognize intent: %w", err)
	}

	// 转换为IntentResult格式
	currentIntent := &IntentResult{
		Type:       currentIntentResult.Type,
		Confidence: currentIntentResult.Confidence,
		Parameters: currentIntentResult.Parameters,
		Command:    currentIntentResult.Command,
	}

	// 分析意图连续性
	continuity := icm.analyzeContinuity(enhancedCtx, currentIntent)

	// 更新意图链
	icm.updateIntentChain(continuity, currentIntent)

	// 检查待处理动作
	icm.processPendingActions(continuity)

	// 生成建议
	suggestions := icm.generateSuggestions(continuity, enhancedCtx)

	result := &IntentContinuityResult{
		SessionID:        sessionID,
		CurrentIntent:    currentIntent.Type,
		Confidence:       currentIntent.Confidence,
		ContinuityScore:  icm.calculateContinuityScore(continuity),
		IntentChain:      continuity.IntentChain,
		PendingActions:   continuity.PendingActions,
		Suggestions:      suggestions,
		ContextVariables: continuity.ContextVariables,
		RequiresFollowUp: icm.requiresFollowUp(continuity),
		NextSteps:        icm.generateNextSteps(continuity),
	}

	return result, nil
}

// analyzeContinuity 分析连续性
func (icm *IntentContinuityManager) analyzeContinuity(enhancedCtx *EnhancedConversationContext, currentIntent *IntentResult) *IntentContinuity {
	continuity := &IntentContinuity{
		SessionID:        enhancedCtx.SessionID,
		CurrentIntent:    currentIntent.Type,
		IntentChain:      []IntentChainNode{},
		PendingActions:   []PendingAction{},
		ContextVariables: make(map[string]interface{}),
		LastUpdate:       time.Now(),
	}

	// 从意图历史构建意图链
	for _, intentRecord := range enhancedCtx.IntentHistory {
		node := IntentChainNode{
			Intent:     intentRecord.Intent,
			Confidence: intentRecord.Confidence,
			Parameters: intentRecord.Parameters,
			Status:     icm.determineIntentStatus(intentRecord, enhancedCtx),
			StartTime:  intentRecord.Timestamp,
		}

		if intentRecord.Resolved {
			endTime := icm.findIntentEndTime(intentRecord, enhancedCtx)
			node.EndTime = &endTime
			node.Status = "completed"
		}

		continuity.IntentChain = append(continuity.IntentChain, node)
	}

	// 分析上下文变量
	icm.extractContextVariables(continuity, enhancedCtx)

	return continuity
}

// updateIntentChain 更新意图链
func (icm *IntentContinuityManager) updateIntentChain(continuity *IntentContinuity, currentIntent *IntentResult) {
	// 检查是否是新意图
	isNewIntent := true
	for i := range continuity.IntentChain {
		if continuity.IntentChain[i].Intent == currentIntent.Type &&
			continuity.IntentChain[i].Status == "pending" {
			// 更新现有意图
			continuity.IntentChain[i].Status = "in_progress"
			continuity.IntentChain[i].Confidence = currentIntent.Confidence
			continuity.IntentChain[i].Parameters = currentIntent.Parameters
			isNewIntent = false
			break
		}
	}

	if isNewIntent {
		// 添加新意图节点
		node := IntentChainNode{
			Intent:     currentIntent.Type,
			Confidence: currentIntent.Confidence,
			Parameters: currentIntent.Parameters,
			Status:     "in_progress",
			StartTime:  time.Now(),
		}
		continuity.IntentChain = append(continuity.IntentChain, node)
	}

	continuity.LastUpdate = time.Now()
}

// processPendingActions 处理待处理动作
func (icm *IntentContinuityManager) processPendingActions(continuity *IntentContinuity) {
	now := time.Now()

	for i := range continuity.PendingActions {
		action := &continuity.PendingActions[i]

		// 检查是否过期
		if action.DueAt != nil && now.After(*action.DueAt) {
			if action.Retries < action.MaxRetries {
				action.Retries++
				icm.logger.WithFields(logrus.Fields{
					"action_id": action.ID,
					"retries":   action.Retries,
				}).Warn("Retrying pending action")
			} else {
				icm.logger.WithFields(logrus.Fields{
					"action_id": action.ID,
				}).Error("Pending action exceeded max retries")
			}
		}
	}
}

// generateSuggestions 生成建议
func (icm *IntentContinuityManager) generateSuggestions(continuity *IntentContinuity, enhancedCtx *EnhancedConversationContext) []IntentSuggestion {
	suggestions := []IntentSuggestion{}

	// 基于意图历史生成建议
	if len(continuity.IntentChain) > 0 {
		lastIntent := continuity.IntentChain[len(continuity.IntentChain)-1]

		switch lastIntent.Intent {
		case "system_check":
			if lastIntent.Status == "completed" {
				suggestions = append(suggestions, IntentSuggestion{
					Type:        "follow_up",
					Description: "建议查看详细的系统日志",
					Intent:      "log_analysis",
					Priority:    7,
				})
			}
		case "host_management":
			suggestions = append(suggestions, IntentSuggestion{
				Type:        "related",
				Description: "可以检查主机性能指标",
				Intent:      "performance_check",
				Priority:    6,
			})
		case "error_investigation":
			suggestions = append(suggestions, IntentSuggestion{
				Type:        "next_step",
				Description: "建议执行系统健康检查",
				Intent:      "system_health",
				Priority:    8,
			})
		}
	}

	// 基于话题流转生成建议
	if len(enhancedCtx.TopicFlow) > 0 {
		lastTransition := enhancedCtx.TopicFlow[len(enhancedCtx.TopicFlow)-1]
		if lastTransition.ToTopic == "troubleshooting" {
			suggestions = append(suggestions, IntentSuggestion{
				Type:        "context",
				Description: "可以查看相关的错误日志",
				Intent:      "error_logs",
				Priority:    7,
			})
		}
	}

	return suggestions
}

// calculateContinuityScore 计算连续性分数
func (icm *IntentContinuityManager) calculateContinuityScore(continuity *IntentContinuity) float64 {
	if len(continuity.IntentChain) == 0 {
		return 0.0
	}

	score := 0.0
	totalWeight := 0.0

	// 基于意图完成率
	completedIntents := 0
	for _, node := range continuity.IntentChain {
		if node.Status == "completed" {
			completedIntents++
		}
	}

	completionRate := float64(completedIntents) / float64(len(continuity.IntentChain))
	score += completionRate * 0.4
	totalWeight += 0.4

	// 基于意图相关性
	relatednessScore := icm.calculateIntentRelatedness(continuity.IntentChain)
	score += relatednessScore * 0.3
	totalWeight += 0.3

	// 基于时间连续性
	timeScore := icm.calculateTimeContinuity(continuity.IntentChain)
	score += timeScore * 0.3
	totalWeight += 0.3

	if totalWeight > 0 {
		return score / totalWeight
	}
	return 0.0
}

// calculateIntentRelatedness 计算意图相关性
func (icm *IntentContinuityManager) calculateIntentRelatedness(chain []IntentChainNode) float64 {
	if len(chain) < 2 {
		return 1.0
	}

	relatedPairs := 0
	totalPairs := len(chain) - 1

	for i := 0; i < len(chain)-1; i++ {
		if icm.areIntentsRelated(chain[i].Intent, chain[i+1].Intent) {
			relatedPairs++
		}
	}

	return float64(relatedPairs) / float64(totalPairs)
}

// calculateTimeContinuity 计算时间连续性
func (icm *IntentContinuityManager) calculateTimeContinuity(chain []IntentChainNode) float64 {
	if len(chain) < 2 {
		return 1.0
	}

	continuousTransitions := 0
	totalTransitions := len(chain) - 1

	for i := 0; i < len(chain)-1; i++ {
		timeDiff := chain[i+1].StartTime.Sub(chain[i].StartTime)
		if timeDiff <= 10*time.Minute { // 10分钟内认为是连续的
			continuousTransitions++
		}
	}

	return float64(continuousTransitions) / float64(totalTransitions)
}

// areIntentsRelated 检查意图是否相关
func (icm *IntentContinuityManager) areIntentsRelated(intent1, intent2 string) bool {
	relatedGroups := [][]string{
		{"system_check", "performance_check", "system_health"},
		{"host_management", "host_status", "host_monitoring"},
		{"log_analysis", "error_logs", "error_investigation"},
		{"security_check", "security_audit", "vulnerability_scan"},
	}

	for _, group := range relatedGroups {
		hasIntent1 := false
		hasIntent2 := false

		for _, intent := range group {
			if intent == intent1 {
				hasIntent1 = true
			}
			if intent == intent2 {
				hasIntent2 = true
			}
		}

		if hasIntent1 && hasIntent2 {
			return true
		}
	}

	return false
}

// 辅助方法
func (icm *IntentContinuityManager) determineIntentStatus(intentRecord IntentRecord, enhancedCtx *EnhancedConversationContext) string {
	if intentRecord.Resolved {
		return "completed"
	}

	// 检查是否有相关的工具调用
	for _, msg := range enhancedCtx.Messages {
		if msg.Timestamp.After(intentRecord.Timestamp) && len(msg.ToolCalls) > 0 {
			return "in_progress"
		}
	}

	return "pending"
}

func (icm *IntentContinuityManager) findIntentEndTime(intentRecord IntentRecord, enhancedCtx *EnhancedConversationContext) time.Time {
	for _, msg := range enhancedCtx.Messages {
		if msg.Timestamp.After(intentRecord.Timestamp) && msg.Role == "assistant" {
			return msg.Timestamp
		}
	}
	return intentRecord.Timestamp
}

func (icm *IntentContinuityManager) extractContextVariables(continuity *IntentContinuity, enhancedCtx *EnhancedConversationContext) {
	// 从消息中提取关键变量
	for _, msg := range enhancedCtx.Messages {
		if msg.Entities != nil {
			for key, value := range msg.Entities {
				continuity.ContextVariables[key] = value
			}
		}
	}

	// 从用户偏好中提取
	for key, value := range enhancedCtx.UserPreferences {
		continuity.ContextVariables[key] = value
	}
}

func (icm *IntentContinuityManager) requiresFollowUp(continuity *IntentContinuity) bool {
	// 检查是否有未完成的意图
	for _, node := range continuity.IntentChain {
		if node.Status == "pending" || node.Status == "in_progress" {
			return true
		}
	}

	// 检查是否有待处理动作
	return len(continuity.PendingActions) > 0
}

func (icm *IntentContinuityManager) generateNextSteps(continuity *IntentContinuity) []string {
	steps := []string{}

	// 基于未完成的意图生成步骤
	for _, node := range continuity.IntentChain {
		if node.Status == "pending" {
			steps = append(steps, fmt.Sprintf("完成%s相关操作", node.Intent))
		}
	}

	// 基于待处理动作生成步骤
	for _, action := range continuity.PendingActions {
		steps = append(steps, action.Description)
	}

	return steps
}

// IntentContinuityResult 意图连续性结果
type IntentContinuityResult struct {
	SessionID        string                 `json:"session_id"`
	CurrentIntent    string                 `json:"current_intent"`
	Confidence       float64                `json:"confidence"`
	ContinuityScore  float64                `json:"continuity_score"`
	IntentChain      []IntentChainNode      `json:"intent_chain"`
	PendingActions   []PendingAction        `json:"pending_actions"`
	Suggestions      []IntentSuggestion     `json:"suggestions"`
	ContextVariables map[string]interface{} `json:"context_variables"`
	RequiresFollowUp bool                   `json:"requires_follow_up"`
	NextSteps        []string               `json:"next_steps"`
}

// IntentSuggestion 意图建议
type IntentSuggestion struct {
	Type        string `json:"type"` // follow_up, related, next_step, context
	Description string `json:"description"`
	Intent      string `json:"intent"`
	Priority    int    `json:"priority"`
}
