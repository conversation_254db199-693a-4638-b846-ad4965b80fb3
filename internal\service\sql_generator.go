package service

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SQLGenerator SQL生成器
type SQLGenerator struct {
	db     *gorm.DB
	logger *logrus.Logger
	schema *DatabaseSchema
}

// NewSQLGenerator 创建SQL生成器
func NewSQLGenerator(db *gorm.DB, logger *logrus.Logger) *SQLGenerator {
	generator := &SQLGenerator{
		db:     db,
		logger: logger,
		schema: NewDatabaseSchema(db),
	}

	// 初始化数据库模式
	if err := generator.schema.LoadSchema(); err != nil {
		logger.WithError(err).Error("Failed to load database schema")
	} else {
		logger.WithField("tables_loaded", len(generator.schema.tables)).Info("Database schema loaded successfully")
		for tableName, table := range generator.schema.tables {
			logger.WithFields(logrus.Fields{
				"table":  tableName,
				"fields": len(table.Fields),
			}).Debug("Table schema loaded")
		}
	}

	return generator
}

// GenerateSQL 生成SQL语句
func (g *SQLGenerator) GenerateSQL(ctx context.Context, request *DatabaseOperationRequest) (*SQLGenerationResult, error) {
	// 验证表名
	if !g.schema.IsValidTable(request.TableName) {
		return nil, fmt.Errorf("invalid table name: %s", request.TableName)
	}

	var sql string
	var estimatedRows int64
	var err error

	switch request.OperationType {
	case "select":
		sql, estimatedRows, err = g.generateSelectSQL(request)
	case "insert":
		sql, err = g.generateInsertSQL(request)
		estimatedRows = 1
	case "update":
		sql, estimatedRows, err = g.generateUpdateSQL(request)
	case "delete":
		sql, estimatedRows, err = g.generateDeleteSQL(request)
	case "describe":
		sql, err = g.generateDescribeSQL(request)
		estimatedRows = 1
	default:
		return nil, fmt.Errorf("unsupported operation type: %s", request.OperationType)
	}

	if err != nil {
		return nil, err
	}

	return &SQLGenerationResult{
		SQL:           sql,
		EstimatedRows: estimatedRows,
		TableName:     request.TableName,
		OperationType: request.OperationType,
	}, nil
}

// generateSelectSQL 生成SELECT语句
func (g *SQLGenerator) generateSelectSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	// 构建SELECT子句
	selectClause := "SELECT *"

	// 构建FROM子句
	fromClause := fmt.Sprintf("FROM %s", request.TableName)

	// 构建WHERE子句
	whereClause, args := g.buildWhereClause(request.Conditions, table)

	// 构建LIMIT子句
	limit := request.Limit
	if limit <= 0 {
		limit = 10 // 默认限制
	}
	if limit > 100 {
		limit = 100 // 最大限制
	}
	limitClause := fmt.Sprintf("LIMIT %d", limit)

	// 组合SQL
	sql := fmt.Sprintf("%s %s", selectClause, fromClause)
	if whereClause != "" {
		sql += " " + whereClause
	}
	sql += " " + limitClause

	// 估算行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate row count")
		estimatedRows = int64(limit)
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"args":           args,
		"estimated_rows": estimatedRows,
	}).Debug("Generated SELECT SQL")

	return sql, estimatedRows, nil
}

// generateInsertSQL 生成INSERT语句
func (g *SQLGenerator) generateInsertSQL(request *DatabaseOperationRequest) (string, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Data) == 0 {
		return "", fmt.Errorf("no data provided for insert")
	}

	// 验证字段
	for field := range request.Data {
		if !table.HasField(field) {
			g.logger.WithFields(logrus.Fields{
				"field":            field,
				"table":            request.TableName,
				"available_fields": table.Fields,
			}).Error("Field validation failed")
			return "", fmt.Errorf("invalid field: %s", field)
		}
	}

	// 构建字段列表和值列表
	var fields []string
	var values []string

	for field, value := range request.Data {
		fields = append(fields, field)
		values = append(values, g.formatValue(value))
	}

	sql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		request.TableName,
		strings.Join(fields, ", "),
		strings.Join(values, ", "))

	g.logger.WithField("sql", sql).Debug("Generated INSERT SQL")
	return sql, nil
}

// generateUpdateSQL 生成UPDATE语句
func (g *SQLGenerator) generateUpdateSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Data) == 0 {
		return "", 0, fmt.Errorf("no data provided for update")
	}

	if len(request.Conditions) == 0 {
		return "", 0, fmt.Errorf("no conditions provided for update (safety check)")
	}

	// 验证字段
	for field := range request.Data {
		if !table.HasField(field) {
			return "", 0, fmt.Errorf("invalid field: %s", field)
		}
	}

	// 构建SET子句
	var setParts []string
	for field, value := range request.Data {
		setParts = append(setParts, fmt.Sprintf("%s = %s", field, g.formatValue(value)))
	}
	setClause := "SET " + strings.Join(setParts, ", ")

	// 构建WHERE子句
	whereClause, _ := g.buildWhereClause(request.Conditions, table)
	if whereClause == "" {
		return "", 0, fmt.Errorf("invalid conditions for update")
	}

	sql := fmt.Sprintf("UPDATE %s %s %s", request.TableName, setClause, whereClause)

	// 估算影响行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate affected rows")
		estimatedRows = 1
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"estimated_rows": estimatedRows,
	}).Debug("Generated UPDATE SQL")

	return sql, estimatedRows, nil
}

// generateDeleteSQL 生成DELETE语句
func (g *SQLGenerator) generateDeleteSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Conditions) == 0 {
		return "", 0, fmt.Errorf("no conditions provided for delete (safety check)")
	}

	// 构建WHERE子句
	whereClause, _ := g.buildWhereClause(request.Conditions, table)
	if whereClause == "" {
		return "", 0, fmt.Errorf("invalid conditions for delete")
	}

	sql := fmt.Sprintf("DELETE FROM %s %s", request.TableName, whereClause)

	// 估算影响行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate affected rows")
		estimatedRows = 1
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"estimated_rows": estimatedRows,
	}).Debug("Generated DELETE SQL")

	return sql, estimatedRows, nil
}

// generateDescribeSQL 生成DESCRIBE语句
func (g *SQLGenerator) generateDescribeSQL(request *DatabaseOperationRequest) (string, error) {
	sql := fmt.Sprintf("PRAGMA table_info(%s)", request.TableName)
	g.logger.WithField("sql", sql).Debug("Generated DESCRIBE SQL")
	return sql, nil
}

// buildWhereClause 构建WHERE子句
func (g *SQLGenerator) buildWhereClause(conditions map[string]interface{}, table *TableSchema) (string, []interface{}) {
	if len(conditions) == 0 {
		return "", nil
	}

	var parts []string
	var args []interface{}

	for field, value := range conditions {
		if !table.HasField(field) {
			continue // 跳过无效字段
		}

		parts = append(parts, fmt.Sprintf("%s = %s", field, g.formatValue(value)))
		args = append(args, value)
	}

	if len(parts) == 0 {
		return "", nil
	}

	return "WHERE " + strings.Join(parts, " AND "), args
}

// formatValue 格式化值
func (g *SQLGenerator) formatValue(value interface{}) string {
	if value == nil {
		return "NULL"
	}

	switch v := value.(type) {
	case string:
		return fmt.Sprintf("'%s'", strings.ReplaceAll(v, "'", "''"))
	case int, int64, float64:
		return fmt.Sprintf("%v", v)
	case bool:
		if v {
			return "1"
		}
		return "0"
	default:
		return fmt.Sprintf("'%v'", v)
	}
}

// estimateRowCount 估算行数
func (g *SQLGenerator) estimateRowCount(tableName string, conditions map[string]interface{}) (int64, error) {
	var count int64

	query := g.db.Table(tableName)
	for field, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	err := query.Count(&count).Error
	return count, err
}

// SQLGenerationResult SQL生成结果
type SQLGenerationResult struct {
	SQL           string `json:"sql"`
	EstimatedRows int64  `json:"estimated_rows"`
	TableName     string `json:"table_name"`
	OperationType string `json:"operation_type"`
}

// DatabaseSchema 数据库模式
type DatabaseSchema struct {
	db     *gorm.DB
	tables map[string]*TableSchema
}

// TableSchema 表模式
type TableSchema struct {
	Name   string            `json:"name"`
	Fields map[string]string `json:"fields"` // field_name -> field_type
}

// NewDatabaseSchema 创建数据库模式
func NewDatabaseSchema(db *gorm.DB) *DatabaseSchema {
	return &DatabaseSchema{
		db:     db,
		tables: make(map[string]*TableSchema),
	}
}

// LoadSchema 加载数据库模式
func (ds *DatabaseSchema) LoadSchema() error {
	// 定义支持的表和字段
	supportedTables := map[string]interface{}{
		"hosts":          &struct{}{}, // 简化的表结构定义
		"users":          &struct{}{},
		"alerts":         &struct{}{},
		"operation_logs": &struct{}{},
		"chat_sessions":  &struct{}{},
		"chat_messages":  &struct{}{},
	}

	for tableName := range supportedTables {
		schema := &TableSchema{
			Name:   tableName,
			Fields: make(map[string]string),
		}

		// 获取表字段信息
		rows, err := ds.db.Raw(fmt.Sprintf("PRAGMA table_info(%s)", tableName)).Rows()
		if err != nil {
			continue // 表不存在，跳过
		}

		for rows.Next() {
			var cid int
			var name, dataType string
			var defaultValue sql.NullString
			var notNull, pk int

			if err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
				continue
			}

			schema.Fields[name] = dataType
		}
		rows.Close()

		ds.tables[tableName] = schema
	}

	return nil
}

// IsValidTable 检查表是否有效
func (ds *DatabaseSchema) IsValidTable(tableName string) bool {
	_, exists := ds.tables[tableName]
	return exists
}

// GetTable 获取表模式
func (ds *DatabaseSchema) GetTable(tableName string) *TableSchema {
	return ds.tables[tableName]
}

// HasField 检查字段是否存在
func (ts *TableSchema) HasField(fieldName string) bool {
	_, exists := ts.Fields[fieldName]
	return exists
}
