package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
)

// HostServiceInterface 主机服务接口（用于密码解密）
type HostServiceInterface interface {
	decryptData(encryptedData string) (string, error)
}

// isBase64 检查字符串是否为base64编码
func isBase64(s string) bool {
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil
}

// SSHConnectionPool SSH连接池
type SSHConnectionPool struct {
	connections map[int64]*SSHConnection
	mutex       sync.RWMutex
	logger      *logrus.Logger
	config      *SSHPoolConfig
	hostService HostServiceInterface // 用于密码解密
}

// SSHConnection SSH连接
type SSHConnection struct {
	HostID    int64
	Client    *ssh.Client
	Session   *ssh.Session
	CreatedAt time.Time
	LastUsed  time.Time
	UseCount  int
	IsHealthy bool
	mutex     sync.RWMutex
}

// SSHPoolConfig SSH连接池配置
type SSHPoolConfig struct {
	MaxConnections      int           `json:"max_connections"`
	MaxIdleTime         time.Duration `json:"max_idle_time"`
	MaxLifetime         time.Duration `json:"max_lifetime"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	ConnectTimeout      time.Duration `json:"connect_timeout"`
	CommandTimeout      time.Duration `json:"command_timeout"`
	MaxRetries          int           `json:"max_retries"`
}

// NewSSHConnectionPool 创建SSH连接池
func NewSSHConnectionPool(logger *logrus.Logger, config *SSHPoolConfig, hostService HostServiceInterface) *SSHConnectionPool {
	if config == nil {
		config = &SSHPoolConfig{
			MaxConnections:      50,
			MaxIdleTime:         5 * time.Minute,
			MaxLifetime:         30 * time.Minute,
			HealthCheckInterval: 1 * time.Minute,
			ConnectTimeout:      10 * time.Second,
			CommandTimeout:      30 * time.Second,
			MaxRetries:          3,
		}
	}

	pool := &SSHConnectionPool{
		connections: make(map[int64]*SSHConnection),
		logger:      logger,
		config:      config,
		hostService: hostService,
	}

	return pool
}

// GetConnection 获取SSH连接
func (pool *SSHConnectionPool) GetConnection(host *model.Host) (*SSHConnection, error) {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	// 检查是否已有连接
	if conn, exists := pool.connections[host.ID]; exists {
		conn.mutex.Lock()
		defer conn.mutex.Unlock()

		// 检查连接是否健康且未过期
		if conn.IsHealthy && time.Since(conn.CreatedAt) < pool.config.MaxLifetime {
			conn.LastUsed = time.Now()
			conn.UseCount++
			return conn, nil
		}

		// 关闭过期或不健康的连接
		pool.closeConnection(conn)
		delete(pool.connections, host.ID)
	}

	// 创建新连接
	conn, err := pool.createConnection(host)
	if err != nil {
		return nil, fmt.Errorf("failed to create SSH connection: %w", err)
	}

	pool.connections[host.ID] = conn
	return conn, nil
}

// createConnection 创建SSH连接
func (pool *SSHConnectionPool) createConnection(host *model.Host) (*SSHConnection, error) {
	// 构建SSH配置
	config := &ssh.ClientConfig{
		User:            host.Username,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(), // 在生产环境中应该验证主机密钥
		Timeout:         pool.config.ConnectTimeout,
	}

	// 添加认证方法
	if host.PasswordEncrypted != "" {
		// 如果密码已加密，需要解密
		password := host.PasswordEncrypted
		// 如果密码看起来是加密的（base64编码），尝试解密
		if len(password) > 20 && isBase64(password) {
			// 使用HostService的解密方法
			if pool.hostService != nil {
				decryptedPassword, err := pool.hostService.decryptData(password)
				if err != nil {
					pool.logger.WithError(err).Warn("Failed to decrypt password, using as-is")
				} else {
					password = decryptedPassword
				}
			} else {
				pool.logger.Warn("HostService not available for password decryption")
			}
		}
		config.Auth = append(config.Auth, ssh.Password(password))
	}

	if host.SSHKeyPath != "" {
		// TODO: 加载SSH私钥
		// key, err := loadPrivateKey(host.SSHKeyPath, host.SSHKeyPassphraseEncrypted)
		// if err != nil {
		//     return nil, fmt.Errorf("failed to load private key: %w", err)
		// }
		// config.Auth = append(config.Auth, ssh.PublicKeys(key))
	}

	// 连接到主机
	address := fmt.Sprintf("%s:%d", host.IPAddress, host.Port)
	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s: %w", address, err)
	}

	// 创建连接对象
	conn := &SSHConnection{
		HostID:    host.ID,
		Client:    client,
		CreatedAt: time.Now(),
		LastUsed:  time.Now(),
		UseCount:  1,
		IsHealthy: true,
	}

	pool.logger.WithFields(logrus.Fields{
		"host_id": host.ID,
		"address": address,
	}).Info("SSH connection created")

	return conn, nil
}

// ExecuteCommand 执行命令
func (pool *SSHConnectionPool) ExecuteCommand(host *model.Host, command string, timeout time.Duration) (*CommandResult, error) {
	conn, err := pool.GetConnection(host)
	if err != nil {
		return nil, err
	}

	return pool.executeCommandOnConnection(conn, command, timeout)
}

// executeCommandOnConnection 在连接上执行命令
func (pool *SSHConnectionPool) executeCommandOnConnection(conn *SSHConnection, command string, timeout time.Duration) (*CommandResult, error) {
	conn.mutex.Lock()
	defer conn.mutex.Unlock()

	// 创建新会话
	session, err := conn.Client.NewSession()
	if err != nil {
		conn.IsHealthy = false
		return nil, fmt.Errorf("failed to create session: %w", err)
	}
	defer session.Close()

	// 设置超时
	if timeout == 0 {
		timeout = pool.config.CommandTimeout
	}

	// 创建结果通道
	resultChan := make(chan *CommandResult, 1)
	errorChan := make(chan error, 1)

	// 在goroutine中执行命令
	go func() {
		startTime := time.Now()
		output, err := session.CombinedOutput(command)
		duration := time.Since(startTime)

		if err != nil {
			errorChan <- err
			return
		}

		result := &CommandResult{
			Command:   command,
			Output:    string(output),
			ExitCode:  0,
			Duration:  duration,
			Timestamp: startTime,
		}

		resultChan <- result
	}()

	// 等待结果或超时
	select {
	case result := <-resultChan:
		conn.LastUsed = time.Now()
		pool.logger.WithFields(logrus.Fields{
			"host_id":  conn.HostID,
			"command":  command,
			"duration": result.Duration,
		}).Debug("Command executed successfully")
		return result, nil

	case err := <-errorChan:
		conn.IsHealthy = false
		pool.logger.WithFields(logrus.Fields{
			"host_id": conn.HostID,
			"command": command,
			"error":   err.Error(),
		}).Error("Command execution failed")
		return nil, fmt.Errorf("command execution failed: %w", err)

	case <-time.After(timeout):
		// 超时处理
		session.Signal(ssh.SIGKILL)
		conn.IsHealthy = false
		pool.logger.WithFields(logrus.Fields{
			"host_id": conn.HostID,
			"command": command,
			"timeout": timeout,
		}).Warn("Command execution timeout")
		return nil, fmt.Errorf("command execution timeout after %v", timeout)
	}
}

// CommandResult 命令执行结果
type CommandResult struct {
	Command   string        `json:"command"`
	Output    string        `json:"output"`
	Error     string        `json:"error"`
	ExitCode  int           `json:"exit_code"`
	Duration  time.Duration `json:"duration"`
	Timestamp time.Time     `json:"timestamp"`
}

// TestConnection 测试连接
func (pool *SSHConnectionPool) TestConnection(host *model.Host) (*ConnectionTestResult, error) {
	startTime := time.Now()

	conn, err := pool.GetConnection(host)
	if err != nil {
		return &ConnectionTestResult{
			Success:  false,
			Message:  fmt.Sprintf("Connection failed: %v", err),
			Duration: time.Since(startTime),
		}, nil
	}

	// 执行简单的测试命令
	result, err := pool.executeCommandOnConnection(conn, "echo 'connection_test'", 5*time.Second)
	duration := time.Since(startTime)

	if err != nil {
		return &ConnectionTestResult{
			Success:  false,
			Message:  fmt.Sprintf("Test command failed: %v", err),
			Duration: duration,
		}, nil
	}

	return &ConnectionTestResult{
		Success:  true,
		Message:  "Connection successful",
		Duration: duration,
		Output:   result.Output,
	}, nil
}

// ConnectionTestResult 连接测试结果
type ConnectionTestResult struct {
	Success  bool          `json:"success"`
	Message  string        `json:"message"`
	Duration time.Duration `json:"duration"`
	Output   string        `json:"output,omitempty"`
}

// CloseConnection 关闭指定主机的连接
func (pool *SSHConnectionPool) CloseConnection(hostID int64) error {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	if conn, exists := pool.connections[hostID]; exists {
		pool.closeConnection(conn)
		delete(pool.connections, hostID)
		pool.logger.WithField("host_id", hostID).Info("SSH connection closed")
		return nil
	}

	return fmt.Errorf("connection not found for host %d", hostID)
}

// closeConnection 关闭连接
func (pool *SSHConnectionPool) closeConnection(conn *SSHConnection) {
	if conn.Session != nil {
		conn.Session.Close()
	}
	if conn.Client != nil {
		conn.Client.Close()
	}
}

// CleanupIdleConnections 清理空闲连接
func (pool *SSHConnectionPool) CleanupIdleConnections() {
	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	now := time.Now()
	for hostID, conn := range pool.connections {
		conn.mutex.RLock()
		isIdle := now.Sub(conn.LastUsed) > pool.config.MaxIdleTime
		isExpired := now.Sub(conn.CreatedAt) > pool.config.MaxLifetime
		conn.mutex.RUnlock()

		if isIdle || isExpired {
			pool.closeConnection(conn)
			delete(pool.connections, hostID)
			pool.logger.WithFields(logrus.Fields{
				"host_id":    hostID,
				"idle":       isIdle,
				"expired":    isExpired,
				"last_used":  conn.LastUsed,
				"created_at": conn.CreatedAt,
			}).Info("SSH connection cleaned up")
		}
	}
}

// HealthCheck 健康检查
func (pool *SSHConnectionPool) HealthCheck() {
	pool.mutex.RLock()
	connections := make([]*SSHConnection, 0, len(pool.connections))
	for _, conn := range pool.connections {
		connections = append(connections, conn)
	}
	pool.mutex.RUnlock()

	for _, conn := range connections {
		go pool.checkConnectionHealth(conn)
	}
}

// checkConnectionHealth 检查连接健康状态
func (pool *SSHConnectionPool) checkConnectionHealth(conn *SSHConnection) {
	conn.mutex.Lock()
	defer conn.mutex.Unlock()

	// 执行简单的健康检查命令
	session, err := conn.Client.NewSession()
	if err != nil {
		conn.IsHealthy = false
		pool.logger.WithFields(logrus.Fields{
			"host_id": conn.HostID,
			"error":   err.Error(),
		}).Warn("SSH connection health check failed")
		return
	}
	defer session.Close()

	// 设置短超时
	done := make(chan error, 1)
	go func() {
		_, err := session.CombinedOutput("echo 'health_check'")
		done <- err
	}()

	select {
	case err := <-done:
		conn.IsHealthy = (err == nil)
		if err != nil {
			pool.logger.WithFields(logrus.Fields{
				"host_id": conn.HostID,
				"error":   err.Error(),
			}).Warn("SSH connection health check command failed")
		}
	case <-time.After(5 * time.Second):
		conn.IsHealthy = false
		session.Signal(ssh.SIGKILL)
		pool.logger.WithField("host_id", conn.HostID).Warn("SSH connection health check timeout")
	}
}

// GetPoolStats 获取连接池统计
func (pool *SSHConnectionPool) GetPoolStats() map[string]interface{} {
	pool.mutex.RLock()
	defer pool.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_connections": len(pool.connections),
		"max_connections":   pool.config.MaxConnections,
	}

	healthyCount := 0
	totalUseCount := 0
	for _, conn := range pool.connections {
		conn.mutex.RLock()
		if conn.IsHealthy {
			healthyCount++
		}
		totalUseCount += conn.UseCount
		conn.mutex.RUnlock()
	}

	stats["healthy_connections"] = healthyCount
	stats["total_use_count"] = totalUseCount

	return stats
}

// StartMaintenanceRoutine 启动维护例程
func (pool *SSHConnectionPool) StartMaintenanceRoutine(ctx context.Context) {
	// 清理例程
	cleanupTicker := time.NewTicker(pool.config.MaxIdleTime)
	defer cleanupTicker.Stop()

	// 健康检查例程
	healthTicker := time.NewTicker(pool.config.HealthCheckInterval)
	defer healthTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			pool.logger.Info("SSH pool maintenance routine stopped")
			return
		case <-cleanupTicker.C:
			pool.CleanupIdleConnections()
		case <-healthTicker.C:
			pool.HealthCheck()
		}
	}
}
