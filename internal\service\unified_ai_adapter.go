package service

import (
	"context"
	"strings"
	"time"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// UnifiedAIAdapter 统一AI服务适配器 - 将新的统一AI服务适配到现有接口
type UnifiedAIAdapter struct {
	unifiedService *ai.UnifiedAIService
	db             *gorm.DB
	config         *config.Config
	logger         *logrus.Logger
}

// NewUnifiedAIAdapter 创建统一AI服务适配器
func NewUnifiedAIAdapter(
	db *gorm.DB,
	cfg *config.Config,
	logger *logrus.Logger,
	hostService HostService,
) AIService {
	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建统一AI服务
	unifiedService := ai.NewUnifiedAIService(deepseekService, hostService, logger)

	return &UnifiedAIAdapter{
		unifiedService: unifiedService,
		db:             db,
		config:         cfg,
		logger:         logger,
	}
}

// ProcessMessage 处理消息
func (uaa *UnifiedAIAdapter) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	uaa.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("UnifiedAIAdapter: Processing message")

	// 转换请求格式
	unifiedReq := &ai.UnifiedAIRequest{
		SessionID: req.SessionID,
		UserID:    req.UserID,
		Message:   req.Message,
		Context:   make(map[string]interface{}),
		IPAddress: "127.0.0.1", // 默认值
		UserAgent: "aiops-platform",
		Options: &ai.RequestOptions{
			EnableCache:         true,
			EnableSecurity:      true,
			EnableFallback:      true,
			Timeout:             30 * time.Second,
			ConfidenceThreshold: 0.8,
		},
		Timestamp: time.Now(),
	}

	// 使用统一AI服务处理
	response, err := uaa.unifiedService.ProcessMessage(ctx, unifiedReq)
	if err != nil {
		uaa.logger.WithError(err).Error("UnifiedAIAdapter: Unified service failed")
		return uaa.createFallbackResponse(req, err, start), nil
	}

	// 转换响应格式
	return &ProcessMessageResponse{
		Content:        response.Message,
		Intent:         uaa.extractIntentType(response),
		Confidence:     response.Confidence,
		Parameters:     uaa.extractParameters(response),
		TokenCount:     uaa.estimateTokenCount(response.Message),
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}, nil
}

// ProcessMessageWithTools 使用工具处理消息
func (uaa *UnifiedAIAdapter) ProcessMessageWithTools(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 直接调用ProcessMessage，因为统一服务已经包含了工具处理
	return uaa.ProcessMessage(ctx, req)
}

// ExtractIntent 提取意图
func (uaa *UnifiedAIAdapter) ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error) {
	// 使用统一服务进行意图提取
	unifiedReq := &ai.UnifiedAIRequest{
		SessionID: "intent_extraction",
		UserID:    1,
		Message:   message,
		Context:   make(map[string]interface{}),
		Options: &ai.RequestOptions{
			EnableCache:    false,
			EnableSecurity: false,
			EnableFallback: true,
			Timeout:        15 * time.Second,
		},
		Timestamp: time.Now(),
	}

	response, err := uaa.unifiedService.ProcessMessage(ctx, unifiedReq)
	if err != nil {
		uaa.logger.WithError(err).Warn("UnifiedAIAdapter: Intent extraction failed")
		return uaa.createFallbackIntent(message), nil
	}

	return &IntentResult{
		Type:       uaa.extractIntentType(response),
		Confidence: response.Confidence,
		Parameters: uaa.extractParameters(response),
		Command:    uaa.extractCommand(response),
	}, nil
}

// 实现其他AIService接口方法
func (uaa *UnifiedAIAdapter) CreateContext(sessionID string, userID int64) error {
	// 简单实现
	return nil
}

func (uaa *UnifiedAIAdapter) GetContext(sessionID string) (*ConversationContext, error) {
	// 返回空上下文
	return &ConversationContext{
		SessionID: sessionID,
		Variables: make(map[string]interface{}),
	}, nil
}

func (uaa *UnifiedAIAdapter) UpdateContext(sessionID string, updates map[string]interface{}) error {
	// 简单实现
	return nil
}

func (uaa *UnifiedAIAdapter) ClearContext(sessionID string) error {
	// 简单实现
	return nil
}

func (uaa *UnifiedAIAdapter) GetAvailableTools(userID int64) ([]ToolDefinition, error) {
	// 返回基本工具列表
	return []ToolDefinition{
		{
			Name:        "host_management",
			Description: "主机管理工具",
			Parameters:  make(map[string]interface{}),
		},
		{
			Name:        "troubleshooting",
			Description: "故障诊断工具",
			Parameters:  make(map[string]interface{}),
		},
		{
			Name:        "monitoring",
			Description: "监控工具",
			Parameters:  make(map[string]interface{}),
		},
	}, nil
}

func (uaa *UnifiedAIAdapter) ExecuteTool(ctx context.Context, toolCall *ToolCall, context *ConversationContext) (*ToolResult, error) {
	// 基本工具执行
	return &ToolResult{
		Success: true,
		Data:    "工具执行成功",
	}, nil
}

func (uaa *UnifiedAIAdapter) GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error) {
	// 使用ProcessMessage实现
	processReq := &ProcessMessageRequest{
		SessionID: "generate_response",
		UserID:    1,
		Message:   req.UserMessage,
	}

	response, err := uaa.ProcessMessage(ctx, processReq)
	if err != nil {
		return nil, err
	}

	return &GenerateResponseResult{
		Content:      response.Content,
		TokenCount:   response.TokenCount,
		FinishReason: "stop",
	}, nil
}

func (uaa *UnifiedAIAdapter) SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error) {
	// 简单实现
	return &ConversationSummary{
		SessionID:   sessionID,
		Summary:     "对话摘要",
		KeyPoints:   []string{"关键点1", "关键点2"},
		GeneratedAt: time.Now(),
	}, nil
}

func (uaa *UnifiedAIAdapter) ValidateCommand(ctx context.Context, command string, context *ConversationContext) (*CommandValidation, error) {
	// 简单验证
	return &CommandValidation{
		Command:     command,
		IsSafe:      true,
		RiskLevel:   "low",
		Risks:       []string{},
		Suggestions: []string{},
		ValidatedAt: time.Now(),
	}, nil
}

func (uaa *UnifiedAIAdapter) ProcessMessageWithWorkflow(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 直接调用ProcessMessage
	return uaa.ProcessMessage(ctx, req)
}

func (uaa *UnifiedAIAdapter) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*WorkflowIntentAnalysis, error) {
	// 简单工作流分析
	return &WorkflowIntentAnalysis{
		NeedsWorkflow:   false,
		WorkflowType:    "none",
		Confidence:      0.5,
		Intent:          "general",
		SuggestedAction: "continue_conversation",
		Parameters:      make(map[string]interface{}),
		Context:         "normal_conversation",
	}, nil
}

func (uaa *UnifiedAIAdapter) GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error) {
	// 简单工作流指导
	return &WorkflowGuidance{
		Message:           "请继续您的操作",
		Suggestions:       []string{"继续", "帮助"},
		NextAction:        "continue",
		RequiredInputs:    []string{},
		ProgressIndicator: "进行中",
		HelpText:          "如需帮助，请输入'帮助'",
	}, nil
}

// 辅助方法
func (uaa *UnifiedAIAdapter) extractIntentType(response *ai.UnifiedAIResponse) string {
	if response.IntentInfo != nil {
		return response.IntentInfo.Type
	}
	return "general_chat"
}

func (uaa *UnifiedAIAdapter) extractParameters(response *ai.UnifiedAIResponse) map[string]interface{} {
	if response.Data != nil {
		return response.Data
	}
	return make(map[string]interface{})
}

func (uaa *UnifiedAIAdapter) extractCommand(response *ai.UnifiedAIResponse) string {
	if len(response.Actions) > 0 {
		return response.Actions[0].Description
	}
	return ""
}

func (uaa *UnifiedAIAdapter) estimateTokenCount(content string) int {
	// 简单的token估算：大约每4个字符一个token
	return len(content) / 4
}

func (uaa *UnifiedAIAdapter) createFallbackResponse(req *ProcessMessageRequest, err error, start time.Time) *ProcessMessageResponse {
	return &ProcessMessageResponse{
		Content:    "🤖 系统正在使用统一AI服务处理您的请求，请稍候...",
		Intent:     "unified_fallback",
		Confidence: 0.7,
		Parameters: map[string]interface{}{
			"original_message": req.Message,
			"fallback_reason":  err.Error(),
			"unified_mode":     true,
		},
		TokenCount:     30,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}
}

func (uaa *UnifiedAIAdapter) createFallbackIntent(message string) *IntentResult {
	// 简单的本地意图识别
	messageLower := strings.ToLower(message)

	if strings.Contains(messageLower, "密码") && strings.Contains(messageLower, "修改") {
		return &IntentResult{
			Type:       "host_management",
			Confidence: 0.8,
			Parameters: map[string]interface{}{
				"operation": "update_password",
				"message":   message,
			},
			Command: "修改主机密码",
		}
	}

	if strings.Contains(messageLower, "主机") && (strings.Contains(messageLower, "添加") || strings.Contains(messageLower, "新增")) {
		return &IntentResult{
			Type:       "host_management",
			Confidence: 0.8,
			Parameters: map[string]interface{}{
				"operation": "add_host",
				"message":   message,
			},
			Command: "添加主机",
		}
	}

	if strings.Contains(messageLower, "列表") || strings.Contains(messageLower, "查看") {
		return &IntentResult{
			Type:       "host_management",
			Confidence: 0.7,
			Parameters: map[string]interface{}{
				"operation": "list_hosts",
				"message":   message,
			},
			Command: "查看主机列表",
		}
	}

	return &IntentResult{
		Type:       "general_chat",
		Confidence: 0.5,
		Parameters: map[string]interface{}{
			"message": message,
		},
		Command: "通用对话",
	}
}

// GetSystemStatus 获取系统状态
func (uaa *UnifiedAIAdapter) GetSystemStatus() map[string]interface{} {
	status := uaa.unifiedService.GetSystemStatus()
	status["adapter_version"] = "1.0.0"
	status["adapter_type"] = "unified_ai_adapter"
	return status
}
