package service

import (
	"aiops-platform/internal/model"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// WebSocketManager WebSocket管理器
type WebSocketManager struct {
	logger      *logrus.Logger
	upgrader    websocket.Upgrader
	connections map[string]*WebSocketConnection
	mutex       sync.RWMutex
	hub         *Hub
	chatService ChatService // 添加聊天服务引用
	aiService   AIService   // 添加AI服务引用（支持意图识别）
}

// WebSocketConnection WebSocket连接
type WebSocketConnection struct {
	ID        string
	UserID    int64
	SessionID string
	Conn      *websocket.Conn
	Send      chan []byte
	Hub       *Hub
	CreatedAt time.Time
	LastPing  time.Time
	mutex     sync.RWMutex
}

// Hub 消息中心
type Hub struct {
	connections map[*WebSocketConnection]bool
	broadcast   chan []byte
	register    chan *WebSocketConnection
	unregister  chan *WebSocketConnection
	logger      *logrus.Logger
	mutex       sync.RWMutex
}

// WSMessage WebSocket消息
type WSMessage struct {
	Type      string                 `json:"type"`
	Data      interface{}            `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	SessionID string                 `json:"session_id,omitempty"`
	UserID    int64                  `json:"user_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ExecutionStatusMessage 执行状态消息
type ExecutionStatusMessage struct {
	TaskID   string                 `json:"task_id"`
	Status   TaskStatus             `json:"status"`
	Progress int                    `json:"progress"`
	Message  string                 `json:"message"`
	Output   string                 `json:"output,omitempty"`
	Error    string                 `json:"error,omitempty"`
	Duration time.Duration          `json:"duration,omitempty"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// CommandProgressMessage 命令进度消息
type CommandProgressMessage struct {
	Command     string    `json:"command"`
	HostID      int64     `json:"host_id"`
	Status      string    `json:"status"`
	Progress    int       `json:"progress"`
	Output      string    `json:"output"`
	Error       string    `json:"error"`
	StartTime   time.Time `json:"start_time"`
	ElapsedTime string    `json:"elapsed_time"`
}

// SystemNotificationMessage 系统通知消息
type SystemNotificationMessage struct {
	Level   string `json:"level"`
	Title   string `json:"title"`
	Content string `json:"content"`
	Actions []struct {
		ID    string `json:"id"`
		Label string `json:"label"`
		Type  string `json:"type"`
	} `json:"actions,omitempty"`
}

// NewWebSocketManager 创建WebSocket管理器
func NewWebSocketManager(logger *logrus.Logger) *WebSocketManager {
	hub := &Hub{
		connections: make(map[*WebSocketConnection]bool),
		broadcast:   make(chan []byte),
		register:    make(chan *WebSocketConnection),
		unregister:  make(chan *WebSocketConnection),
		logger:      logger,
	}

	manager := &WebSocketManager{
		logger:      logger,
		connections: make(map[string]*WebSocketConnection),
		hub:         hub,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// 在生产环境中应该检查Origin
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	}

	// 启动Hub
	go hub.Run()

	return manager
}

// SetChatService 设置聊天服务
func (wm *WebSocketManager) SetChatService(chatService ChatService) {
	wm.chatService = chatService
}

// SetAIService 设置AI服务
func (wm *WebSocketManager) SetAIService(aiService AIService) {
	wm.aiService = aiService
}

// HandleWebSocket 处理WebSocket连接
func (wm *WebSocketManager) HandleWebSocket(w http.ResponseWriter, r *http.Request, userID int64, sessionID string) error {
	conn, err := wm.upgrader.Upgrade(w, r, nil)
	if err != nil {
		wm.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return err
	}

	// 创建连接对象
	wsConn := &WebSocketConnection{
		ID:        fmt.Sprintf("%d_%s_%d", userID, sessionID, time.Now().Unix()),
		UserID:    userID,
		SessionID: sessionID,
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Hub:       wm.hub,
		CreatedAt: time.Now(),
		LastPing:  time.Now(),
	}

	// 注册连接
	wm.mutex.Lock()
	wm.connections[wsConn.ID] = wsConn
	wm.mutex.Unlock()

	wsConn.Hub.register <- wsConn

	// 启动读写协程
	go wsConn.writePump()
	go wsConn.readPump(wm)

	wm.logger.WithFields(logrus.Fields{
		"connection_id": wsConn.ID,
		"user_id":       userID,
		"session_id":    sessionID,
	}).Info("WebSocket connection established")

	return nil
}

// Run 运行Hub
func (h *Hub) Run() {
	for {
		select {
		case conn := <-h.register:
			h.mutex.Lock()
			h.connections[conn] = true
			h.mutex.Unlock()
			h.logger.WithField("connection_id", conn.ID).Debug("WebSocket connection registered")

		case conn := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.connections[conn]; ok {
				delete(h.connections, conn)
				close(conn.Send)
			}
			h.mutex.Unlock()
			h.logger.WithField("connection_id", conn.ID).Debug("WebSocket connection unregistered")

		case message := <-h.broadcast:
			h.mutex.RLock()
			for conn := range h.connections {
				select {
				case conn.Send <- message:
				default:
					delete(h.connections, conn)
					close(conn.Send)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// readPump 读取消息
func (c *WebSocketConnection) readPump(wm *WebSocketManager) {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
		wm.removeConnection(c.ID)
	}()

	// 设置读取限制
	c.Conn.SetReadLimit(512)
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.mutex.Lock()
		c.LastPing = time.Now()
		c.mutex.Unlock()
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		_, message, err := c.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				wm.logger.WithError(err).Error("WebSocket read error")
			}
			break
		}

		// 处理客户端消息
		wm.handleClientMessage(c, message)
	}
}

// writePump 写入消息
func (c *WebSocketConnection) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// 批量发送队列中的消息
			n := len(c.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.Send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleClientMessage 处理客户端消息
func (wm *WebSocketManager) handleClientMessage(conn *WebSocketConnection, message []byte) {
	// 首先尝试解析为标准WSMessage格式
	var msg WSMessage
	if err := json.Unmarshal(message, &msg); err != nil {
		wm.logger.WithError(err).Error("Failed to unmarshal client message")
		return
	}

	// 如果data字段为空，尝试从其他字段获取内容（兼容前端格式）
	if msg.Data == nil {
		var frontendMsg struct {
			Type    string `json:"type"`
			Content string `json:"content"`
		}
		if err := json.Unmarshal(message, &frontendMsg); err == nil && frontendMsg.Content != "" {
			msg.Data = frontendMsg.Content
		}
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"message_type":  msg.Type,
	}).Debug("Received client message")

	// 根据消息类型处理
	switch msg.Type {
	case "ping":
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "pong",
			Data:      "pong",
			Timestamp: time.Now(),
		})
	case "message":
		// 处理聊天消息
		wm.handleChatMessage(conn, msg)
	case "subscribe":
		// 处理订阅请求
		wm.handleSubscription(conn, msg.Data)
	case "unsubscribe":
		// 处理取消订阅请求
		wm.handleUnsubscription(conn, msg.Data)
	default:
		wm.logger.WithField("message_type", msg.Type).Warn("Unknown message type")
	}
}

// handleSubscription 处理订阅
func (wm *WebSocketManager) handleSubscription(conn *WebSocketConnection, data interface{}) {
	// 实现订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"subscription":  data,
	}).Debug("Handling subscription")
}

// handleChatMessage 处理聊天消息
func (wm *WebSocketManager) handleChatMessage(conn *WebSocketConnection, msg WSMessage) {
	// 检查聊天服务是否可用
	if wm.chatService == nil {
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Chat service not available",
			Timestamp: time.Now(),
		})
		return
	}

	// 提取消息内容
	content, ok := msg.Data.(string)
	if !ok {
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Invalid message format",
			Timestamp: time.Now(),
		})
		return
	}

	// 发送用户消息确认
	wm.SendToConnection(conn.ID, &WSMessage{
		Type: "user_message",
		Data: map[string]interface{}{
			"content":    content,
			"created_at": time.Now(),
		},
		Timestamp: time.Now(),
	})

	// 使用智能AI服务处理消息（支持意图识别和执行）
	if wm.aiService != nil {
		wm.handleWithAIService(conn, content)
	} else {
		// 降级到基础聊天服务
		wm.handleWithChatService(conn, content)
	}
}

// handleWithAIService 使用AI服务处理消息（支持意图识别）
func (wm *WebSocketManager) handleWithAIService(conn *WebSocketConnection, content string) {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"user_id":       conn.UserID,
		"message":       content,
	}).Info("WebSocket: Starting AI service message processing")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 构建AI服务请求
	req := &ProcessMessageRequest{
		SessionID: conn.SessionID,
		UserID:    conn.UserID,
		Message:   content,
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Info("WebSocket: Calling AI service ProcessMessage")

	// 调用AI服务处理消息
	response, err := wm.aiService.ProcessMessage(ctx, req)
	if err != nil {
		wm.logger.WithFields(logrus.Fields{
			"connection_id": conn.ID,
			"session_id":    conn.SessionID,
			"error":         err.Error(),
		}).Error("WebSocket: Failed to process message with AI service")

		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Failed to process message",
			Timestamp: time.Now(),
		})
		return
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"content":       response.Content,
		"intent":        response.Intent,
		"confidence":    response.Confidence,
		"token_count":   response.TokenCount,
	}).Info("WebSocket: AI service returned response, preparing to send")

	// 发送AI响应 - 避免循环引用
	responseData := map[string]interface{}{
		"content":     response.Content,
		"intent":      response.Intent,
		"confidence":  response.Confidence,
		"created_at":  response.Timestamp,
		"token_count": response.TokenCount,
	}

	// 安全地添加参数，避免循环引用
	if response.Parameters != nil {
		safeParams := make(map[string]interface{})
		for k, v := range response.Parameters {
			// 只添加基本类型，避免复杂对象的循环引用
			switch val := v.(type) {
			case string, int, int64, float64, bool:
				safeParams[k] = val
			case nil:
				safeParams[k] = nil
			default:
				// 对于复杂类型，转换为字符串
				safeParams[k] = fmt.Sprintf("%v", val)
			}
		}
		responseData["parameters"] = safeParams
	}

	responseMsg := &WSMessage{
		Type:      "assistant_message",
		Data:      responseData,
		Timestamp: time.Now(),
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
		"message_type":  responseMsg.Type,
	}).Info("WebSocket: Sending AI response to connection")

	wm.SendToConnection(conn.ID, responseMsg)

	wm.logger.WithFields(logrus.Fields{
		"connection_id": conn.ID,
		"session_id":    conn.SessionID,
	}).Info("WebSocket: AI response sent successfully")
}

// handleWithChatService 使用基础聊天服务处理消息
func (wm *WebSocketManager) handleWithChatService(conn *WebSocketConnection, content string) {
	// 处理AI响应
	req := &model.ChatMessageRequest{
		SessionID: conn.SessionID,
		Content:   content,
		Stream:    true,
	}

	message, err := wm.chatService.SendMessage(req)
	if err != nil {
		wm.logger.WithError(err).Error("Failed to process chat message")
		wm.SendToConnection(conn.ID, &WSMessage{
			Type:      "error",
			Data:      "Failed to process message",
			Timestamp: time.Now(),
		})
		return
	}

	// 发送AI响应
	wm.SendToConnection(conn.ID, &WSMessage{
		Type:      "assistant_message",
		Data:      message,
		Timestamp: time.Now(),
	})
}

// handleUnsubscription 处理取消订阅
func (wm *WebSocketManager) handleUnsubscription(conn *WebSocketConnection, data interface{}) {
	// 实现取消订阅逻辑
	wm.logger.WithFields(logrus.Fields{
		"connection_id":  conn.ID,
		"unsubscription": data,
	}).Debug("Handling unsubscription")
}

// removeConnection 移除连接
func (wm *WebSocketManager) removeConnection(connectionID string) {
	wm.mutex.Lock()
	defer wm.mutex.Unlock()
	delete(wm.connections, connectionID)
}

// SendToConnection 发送消息到指定连接
func (wm *WebSocketManager) SendToConnection(connectionID string, message *WSMessage) error {
	wm.logger.WithFields(logrus.Fields{
		"connection_id": connectionID,
		"message_type":  message.Type,
	}).Info("WebSocket: Attempting to send message to connection")

	wm.mutex.RLock()
	conn, exists := wm.connections[connectionID]
	wm.mutex.RUnlock()

	if !exists {
		wm.logger.WithFields(logrus.Fields{
			"connection_id": connectionID,
		}).Error("WebSocket: Connection not found")
		return fmt.Errorf("connection not found: %s", connectionID)
	}

	data, err := json.Marshal(message)
	if err != nil {
		wm.logger.WithFields(logrus.Fields{
			"connection_id": connectionID,
			"error":         err.Error(),
		}).Error("WebSocket: Failed to marshal message")
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	wm.logger.WithFields(logrus.Fields{
		"connection_id": connectionID,
		"data_length":   len(data),
	}).Info("WebSocket: Message marshaled, sending to connection")

	select {
	case conn.Send <- data:
		wm.logger.WithFields(logrus.Fields{
			"connection_id": connectionID,
		}).Info("WebSocket: Message sent successfully to connection")
		return nil
	default:
		wm.logger.WithFields(logrus.Fields{
			"connection_id": connectionID,
		}).Error("WebSocket: Connection send buffer full")
		return fmt.Errorf("connection send buffer full")
	}
}

// SendToUser 发送消息到用户的所有连接
func (wm *WebSocketManager) SendToUser(userID int64, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	sent := 0
	for _, conn := range wm.connections {
		if conn.UserID == userID {
			select {
			case conn.Send <- data:
				sent++
			default:
				wm.logger.WithField("connection_id", conn.ID).Warn("Connection send buffer full")
			}
		}
	}

	if sent == 0 {
		return fmt.Errorf("no active connections for user %d", userID)
	}

	return nil
}

// SendToSession 发送消息到会话的所有连接
func (wm *WebSocketManager) SendToSession(sessionID string, message *WSMessage) error {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	sent := 0
	for _, conn := range wm.connections {
		if conn.SessionID == sessionID {
			select {
			case conn.Send <- data:
				sent++
			default:
				wm.logger.WithField("connection_id", conn.ID).Warn("Connection send buffer full")
			}
		}
	}

	if sent == 0 {
		return fmt.Errorf("no active connections for session %s", sessionID)
	}

	return nil
}

// BroadcastToAll 广播消息到所有连接
func (wm *WebSocketManager) BroadcastToAll(message *WSMessage) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	wm.hub.broadcast <- data
	return nil
}

// SendExecutionStatus 发送执行状态
func (wm *WebSocketManager) SendExecutionStatus(sessionID string, status *ExecutionStatusMessage) error {
	message := &WSMessage{
		Type:      "execution_status",
		Data:      status,
		Timestamp: time.Now(),
		SessionID: sessionID,
	}

	return wm.SendToSession(sessionID, message)
}

// SendCommandProgress 发送命令进度
func (wm *WebSocketManager) SendCommandProgress(sessionID string, progress *CommandProgressMessage) error {
	message := &WSMessage{
		Type:      "command_progress",
		Data:      progress,
		Timestamp: time.Now(),
		SessionID: sessionID,
	}

	return wm.SendToSession(sessionID, message)
}

// SendSystemNotification 发送系统通知
func (wm *WebSocketManager) SendSystemNotification(userID int64, notification *SystemNotificationMessage) error {
	message := &WSMessage{
		Type:      "system_notification",
		Data:      notification,
		Timestamp: time.Now(),
		UserID:    userID,
	}

	return wm.SendToUser(userID, message)
}

// GetConnectionStats 获取连接统计
func (wm *WebSocketManager) GetConnectionStats() map[string]interface{} {
	wm.mutex.RLock()
	defer wm.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_connections":      len(wm.connections),
		"connections_by_user":    make(map[int64]int),
		"connections_by_session": make(map[string]int),
	}

	userConnections := make(map[int64]int)
	sessionConnections := make(map[string]int)

	for _, conn := range wm.connections {
		userConnections[conn.UserID]++
		sessionConnections[conn.SessionID]++
	}

	stats["connections_by_user"] = userConnections
	stats["connections_by_session"] = sessionConnections

	return stats
}

// CleanupStaleConnections 清理过期连接
func (wm *WebSocketManager) CleanupStaleConnections(maxIdleTime time.Duration) {
	wm.mutex.Lock()
	defer wm.mutex.Unlock()

	now := time.Now()
	for id, conn := range wm.connections {
		conn.mutex.RLock()
		isStale := now.Sub(conn.LastPing) > maxIdleTime
		conn.mutex.RUnlock()

		if isStale {
			conn.Conn.Close()
			delete(wm.connections, id)
			wm.logger.WithField("connection_id", id).Info("Cleaned up stale WebSocket connection")
		}
	}
}

// StartCleanupRoutine 启动清理例程
func (wm *WebSocketManager) StartCleanupRoutine(ctx context.Context, interval, maxIdleTime time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			wm.logger.Info("WebSocket cleanup routine stopped")
			return
		case <-ticker.C:
			wm.CleanupStaleConnections(maxIdleTime)
		}
	}
}
