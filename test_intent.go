package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"aiops-platform/internal/config"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/service"
)

func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run test_intent.go \"your message\"")
	}

	message := os.Args[1]

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logger.New(cfg.Log)

	// 创建DeepSeek服务
	deepseekService := service.NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建意图识别器
	intentRecognizer := service.NewIntentRecognizer(deepseekService, logger)

	// 测试意图识别
	fmt.Printf("测试消息: %s\n", message)
	fmt.Println("正在调用DeepSeek API进行意图识别...")

	intent, err := intentRecognizer.RecognizeIntent(context.Background(), message)
	if err != nil {
		log.Fatalf("意图识别失败: %v", err)
	}

	fmt.Printf("识别结果:\n")
	fmt.Printf("  意图类型: %s\n", intent.Type)
	fmt.Printf("  置信度: %.2f\n", intent.Confidence)
	fmt.Printf("  参数: %+v\n", intent.Parameters)
	if intent.Command != "" {
		fmt.Printf("  命令: %s\n", intent.Command)
	}
}
