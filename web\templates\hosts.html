<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主机管理 - AI运维平台</title>
    <link href="/static/css/design-system.css" rel="stylesheet">
    <link href="/static/css/interactions.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/fonts.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        .host-status-online { color: #198754; }
        .host-status-offline { color: #dc3545; }
        .host-status-unknown { color: #6c757d; }
        .host-status-error { color: #fd7e14; }
        .host-status-maintenance { color: #0dcaf0; }
        
        .host-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        
        .host-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        .status-online { background-color: #198754; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #6c757d; }
        .status-error { background-color: #fd7e14; }
        .status-maintenance { background-color: #0dcaf0; }
    </style>
</head>
<body>
    <!-- 页面加载进度条 -->
    <div class="loading-bar" id="loading-bar"></div>
    
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-robot"></i>
                AI运维平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/hosts">
                            <i class="bi bi-server"></i>
                            主机管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/alerts">
                            <i class="bi bi-exclamation-triangle"></i>
                            告警管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/chat">
                            <i class="bi bi-chat-dots"></i>
                            AI对话
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stats">
                            <i class="bi bi-graph-up"></i>
                            统计报表
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/users"><i class="bi bi-people"></i> 用户管理</a></li>
                            <li><a class="dropdown-item" href="/config"><i class="bi bi-gear"></i> 系统配置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/login"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 页面标题和操作栏 -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-server text-primary"></i> 主机管理</h2>
                        <p class="text-muted">管理和监控您的服务器主机</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHostModal">
                            <i class="bi bi-plus-circle"></i>
                            添加主机
                        </button>
                        <button class="btn btn-outline-secondary" onclick="refreshHosts()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索栏 -->
        <div class="row mb-4">
            <div class="col">
                <div class="card">
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">状态筛选</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="online">在线</option>
                                    <option value="offline">离线</option>
                                    <option value="unknown">未知</option>
                                    <option value="error">错误</option>
                                    <option value="maintenance">维护中</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">环境筛选</label>
                                <select class="form-select" id="environmentFilter">
                                    <option value="">全部环境</option>
                                    <option value="production">生产环境</option>
                                    <option value="staging">预发布环境</option>
                                    <option value="development">开发环境</option>
                                    <option value="testing">测试环境</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">分组筛选</label>
                                <select class="form-select" id="groupFilter">
                                    <option value="">全部分组</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">搜索</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索主机名、IP地址...">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchHosts()">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主机列表 -->
        <div class="row" id="hostsList">
            <!-- 主机卡片将通过JavaScript动态加载 -->
        </div>

        <!-- 分页 -->
        <div class="row mt-4">
            <div class="col">
                <nav aria-label="主机列表分页">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 添加主机模态框 -->
    <div class="modal fade" id="addHostModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-plus-circle"></i>
                        添加主机
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addHostForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">主机名 *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">IP地址 *</label>
                                <input type="text" class="form-control" name="ip_address" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">SSH端口</label>
                                <input type="number" class="form-control" name="port" value="22" min="1" max="65535">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">用户名 *</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">密码</label>
                                <input type="password" class="form-control" name="password">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">SSH密钥路径</label>
                                <input type="text" class="form-control" name="ssh_key_path" placeholder="如：/home/<USER>/.ssh/id_rsa">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">SSH密钥密码</label>
                                <input type="password" class="form-control" name="ssh_key_passphrase" placeholder="SSH密钥的密码（如果有）">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">环境</label>
                                <select class="form-select" name="environment">
                                    <option value="production">生产环境</option>
                                    <option value="staging">预发布环境</option>
                                    <option value="development">开发环境</option>
                                    <option value="testing">测试环境</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">分组</label>
                                <input type="text" class="form-control" name="group_name" placeholder="如：web, database, cache">
                            </div>
                            <div class="col-12">
                                <label class="form-label">描述</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="monitoring_enabled" checked>
                                    <label class="form-check-label">启用监控</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="backup_enabled">
                                    <label class="form-check-label">启用备份</label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addHost()">
                        <i class="bi bi-plus-circle"></i>
                        添加主机
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHosts();
            loadGroups();
            
            // 绑定筛选器事件
            document.getElementById('statusFilter').addEventListener('change', loadHosts);
            document.getElementById('environmentFilter').addEventListener('change', loadHosts);
            document.getElementById('groupFilter').addEventListener('change', loadHosts);
            
            // 绑定搜索框回车事件
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchHosts();
                }
            });
        });

        // 当前页码
        let currentPage = 1;
        const pageSize = 12;

        // 加载主机列表
        async function loadHosts(page = 1) {
            try {
                showLoading();
                currentPage = page;

                const params = new URLSearchParams({
                    page: page,
                    limit: pageSize
                });

                // 添加筛选条件
                const status = document.getElementById('statusFilter').value;
                const environment = document.getElementById('environmentFilter').value;
                const group = document.getElementById('groupFilter').value;
                const search = document.getElementById('searchInput').value;

                if (status) params.append('status', status);
                if (environment) params.append('environment', environment);
                if (group) params.append('group', group);
                if (search) params.append('search', search);

                const response = await api.get(`/hosts?${params}`);

                if (response.code === 200) {
                    renderHosts(response.data.hosts || []);
                    renderPagination(response.data.pagination || {});
                } else {
                    throw new Error(response.message || '加载主机列表失败');
                }
            } catch (error) {
                console.error('加载主机列表失败:', error);
                showAlert('加载主机列表失败: ' + error.message, 'danger');
            } finally {
                hideLoading();
            }
        }

        // 渲染主机列表
        function renderHosts(hosts) {
            const container = document.getElementById('hostsList');

            if (!hosts || hosts.length === 0) {
                container.innerHTML = `
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="bi bi-server display-1 text-muted"></i>
                            <h4 class="mt-3 text-muted">暂无主机</h4>
                            <p class="text-muted">点击"添加主机"按钮开始添加您的第一台主机</p>
                        </div>
                    </div>
                `;
                return;
            }

            container.innerHTML = hosts.map(host => `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card host-card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <span class="status-indicator status-${host.status}"></span>
                                ${host.name}
                            </h6>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="testConnection(${host.id})">
                                        <i class="bi bi-wifi"></i> 测试连接
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="editHost(${host.id})">
                                        <i class="bi bi-pencil"></i> 编辑
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteHost(${host.id})">
                                        <i class="bi bi-trash"></i> 删除
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-2 small">
                                <div class="col-6">
                                    <strong>IP地址:</strong><br>
                                    <span class="text-muted">${host.ip_address}:${host.port}</span>
                                </div>
                                <div class="col-6">
                                    <strong>状态:</strong><br>
                                    <span class="host-status-${host.status}">${getStatusText(host.status)}</span>
                                </div>
                                <div class="col-6">
                                    <strong>环境:</strong><br>
                                    <span class="text-muted">${getEnvironmentText(host.environment)}</span>
                                </div>
                                <div class="col-6">
                                    <strong>分组:</strong><br>
                                    <span class="text-muted">${host.group_name || '未分组'}</span>
                                </div>
                                ${host.last_connected ? `
                                <div class="col-12">
                                    <strong>最后连接:</strong><br>
                                    <span class="text-muted">${formatDateTime(host.last_connected)}</span>
                                </div>
                                ` : ''}
                            </div>
                            ${host.description ? `
                            <div class="mt-2">
                                <small class="text-muted">${host.description}</small>
                            </div>
                            ` : ''}
                        </div>
                        <div class="card-footer">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">
                                    连接次数: ${host.connection_count || 0}
                                </small>
                                <small class="text-muted">
                                    创建于: ${formatDate(host.created_at)}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 渲染分页
        function renderPagination(pagination) {
            const container = document.getElementById('pagination');

            if (!pagination || pagination.pages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = '';

            // 上一页
            if (pagination.page > 1) {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="loadHosts(${pagination.page - 1})">上一页</a>
                </li>`;
            }

            // 页码
            for (let i = 1; i <= pagination.pages; i++) {
                if (i === pagination.page) {
                    html += `<li class="page-item active">
                        <span class="page-link">${i}</span>
                    </li>`;
                } else {
                    html += `<li class="page-item">
                        <a class="page-link" href="#" onclick="loadHosts(${i})">${i}</a>
                    </li>`;
                }
            }

            // 下一页
            if (pagination.page < pagination.pages) {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="loadHosts(${pagination.page + 1})">下一页</a>
                </li>`;
            }

            container.innerHTML = html;
        }

        // 添加主机
        async function addHost() {
            try {
                const form = document.getElementById('addHostForm');
                const formData = new FormData(form);

                // 基础验证
                const name = formData.get('name')?.trim();
                const ipAddress = formData.get('ip_address')?.trim();
                const username = formData.get('username')?.trim();
                const password = formData.get('password')?.trim();
                const sshKeyPath = formData.get('ssh_key_path')?.trim();

                if (!name) {
                    showAlert('请输入主机名', 'warning');
                    return;
                }

                if (!ipAddress) {
                    showAlert('请输入IP地址', 'warning');
                    return;
                }

                if (!username) {
                    showAlert('请输入用户名', 'warning');
                    return;
                }

                // 验证至少提供一种认证方式
                if (!password && !sshKeyPath) {
                    showAlert('请至少提供密码或SSH密钥路径中的一种认证方式', 'warning');
                    return;
                }

                const data = {
                    name: name,
                    ip_address: ipAddress,
                    port: parseInt(formData.get('port')) || 22,
                    username: username,
                    password: password,
                    ssh_key_path: sshKeyPath,
                    ssh_key_passphrase: formData.get('ssh_key_passphrase')?.trim(),
                    description: formData.get('description')?.trim(),
                    environment: formData.get('environment') || 'production',
                    group_name: formData.get('group_name')?.trim(),
                    monitoring_enabled: formData.get('monitoring_enabled') === 'on',
                    backup_enabled: formData.get('backup_enabled') === 'on'
                };

                // 显示加载状态
                const submitBtn = document.querySelector('#addHostModal .btn-primary');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 添加中...';
                submitBtn.disabled = true;

                const response = await api.post('/hosts', data);

                if (response.code === 201) {
                    showAlert('主机添加成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('addHostModal')).hide();
                    form.reset();
                    loadHosts();
                    loadGroups(); // 重新加载分组列表
                } else {
                    throw new Error(response.message || '添加主机失败');
                }
            } catch (error) {
                console.error('添加主机失败:', error);
                let errorMessage = '添加主机失败';

                if (error.message) {
                    errorMessage += ': ' + error.message;
                }

                // 处理常见错误
                if (error.message?.includes('already exists')) {
                    errorMessage = '主机名或IP地址已存在，请使用不同的名称或IP';
                } else if (error.message?.includes('Invalid request body')) {
                    errorMessage = '请检查输入的数据格式是否正确';
                } else if (error.message?.includes('network')) {
                    errorMessage = '网络连接失败，请检查网络设置';
                }

                showAlert(errorMessage, 'danger');
            } finally {
                // 恢复按钮状态
                const submitBtn = document.querySelector('#addHostModal .btn-primary');
                if (submitBtn) {
                    submitBtn.innerHTML = '<i class="bi bi-plus-circle"></i> 添加主机';
                    submitBtn.disabled = false;
                }
            }
        }

        // 测试连接
        async function testConnection(hostId) {
            try {
                showAlert('正在测试连接...', 'info');

                const response = await api.post(`/hosts/${hostId}/test`);

                if (response.code === 200) {
                    const result = response.data;
                    if (result.success) {
                        showAlert(`连接测试成功 (${result.duration}ms)`, 'success');
                    } else {
                        showAlert(`连接测试失败: ${result.message}`, 'warning');
                    }
                    loadHosts(currentPage); // 刷新列表以更新状态
                } else {
                    throw new Error(response.message || '测试连接失败');
                }
            } catch (error) {
                console.error('测试连接失败:', error);
                showAlert('测试连接失败: ' + error.message, 'danger');
            }
        }

        // 删除主机
        async function deleteHost(hostId) {
            if (!confirm('确定要删除这台主机吗？此操作不可恢复。')) {
                return;
            }

            try {
                const response = await api.delete(`/hosts/${hostId}`);

                if (response.code === 200) {
                    showAlert('主机删除成功', 'success');
                    loadHosts(currentPage);
                } else {
                    throw new Error(response.message || '删除主机失败');
                }
            } catch (error) {
                console.error('删除主机失败:', error);
                showAlert('删除主机失败: ' + error.message, 'danger');
            }
        }

        // 刷新主机列表
        function refreshHosts() {
            loadHosts(currentPage);
        }

        // 搜索主机
        function searchHosts() {
            loadHosts(1); // 搜索时回到第一页
        }

        // 加载分组列表
        async function loadGroups() {
            try {
                const response = await api.get('/hosts?limit=100'); // 获取主机来提取分组

                if (response.code === 200) {
                    const hosts = response.data.hosts || [];
                    const groups = [...new Set(hosts.map(h => h.group_name).filter(g => g))];

                    const groupFilter = document.getElementById('groupFilter');
                    const currentValue = groupFilter.value;

                    groupFilter.innerHTML = '<option value="">全部分组</option>' +
                        groups.map(group => `<option value="${group}">${group}</option>`).join('');

                    groupFilter.value = currentValue; // 保持当前选择
                }
            } catch (error) {
                console.error('加载分组失败:', error);
            }
        }

        // 工具函数
        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'unknown': '未知',
                'error': '错误',
                'maintenance': '维护中'
            };
            return statusMap[status] || status;
        }

        function getEnvironmentText(environment) {
            const envMap = {
                'production': '生产环境',
                'staging': '预发布环境',
                'development': '开发环境',
                'testing': '测试环境'
            };
            return envMap[environment] || environment;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            // 创建提示框
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 显示/隐藏加载状态
        function showLoading() {
            document.getElementById('loading-bar').style.width = '100%';
        }

        function hideLoading() {
            setTimeout(() => {
                document.getElementById('loading-bar').style.width = '0%';
            }, 200);
        }
    </script>
</body>
</html>
