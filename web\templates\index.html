<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维助手 - 智能对话管理平台</title>

    <!-- 本地字体 -->
    <link href="/static/css/fonts.css" rel="stylesheet">

    <!-- 图标 -->
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

    <!-- 现代化设计系统 V2.0 -->
    <link href="/static/css/design-system.css" rel="stylesheet">
    
    <!-- 统一组件库 -->
    <link href="/static/css/components.css" rel="stylesheet">

    <!-- 交互增强系统 -->
    <link href="/static/css/interactions.css" rel="stylesheet">
    
    <!-- AI对话界面专用样式 -->
    <link href="/static/css/chat.css" rel="stylesheet">

    <!-- 主题系统 -->
    <link href="/static/css/themes.css" rel="stylesheet">

    <!-- 高级动画效果 -->
    <link href="/static/css/animations.css" rel="stylesheet">

    <!-- 高级交互功能 -->
    <link href="/static/css/advanced-interactions.css" rel="stylesheet">

    <!-- 性能优化 -->
    <link href="/static/css/performance.css" rel="stylesheet">

    <!-- 可访问性支持 -->
    <link href="/static/css/accessibility.css" rel="stylesheet">
</head>
<body>
    <!-- 跳转链接 -->
    <a href="#main-content" class="skip-link" data-i18n="accessibility.skipToMain">跳转到主内容</a>

    <!-- 状态公告区域 -->
    <div class="live-region" aria-live="polite" id="status-announcer"></div>

    <!-- 页面加载器 -->
    <div class="page-loader" id="page-loader">
        <div class="loader-content">
            <div class="loader-logo">
                <i class="bi bi-robot"></i>
            </div>
            <div class="loader-title" data-i18n="app.title">AI运维助手</div>
            <div class="loader-subtitle" data-i18n="messages.loading">正在加载...</div>
            <div class="loader-progress">
                <div class="loader-progress-bar"></div>
            </div>
            <div class="loader-status" id="loader-status">初始化系统...</div>
        </div>
    </div>

    <div class="chat-container" id="main-content">
        <!-- 顶部导航栏 -->
        <nav class="chat-navbar">
            <a href="/" class="chat-brand">
                <div class="brand-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <span>AI运维助手</span>
            </a>

            <div class="chat-actions">
                <!-- 语言切换器 -->
                <div class="language-switcher">
                    <button class="language-toggle tooltip" data-tooltip="切换语言" onclick="toggleLanguageMenu()">
                        <span class="language-flag" id="current-language-flag">🇨🇳</span>
                        <span id="current-language-code">中文</span>
                        <i class="bi bi-chevron-down"></i>
                    </button>
                    <div class="language-dropdown" id="language-dropdown">
                        <button class="language-option active" onclick="changeLanguage('zh-CN')">
                            <span class="language-flag">🇨🇳</span>
                            <span>简体中文</span>
                        </button>
                        <button class="language-option" onclick="changeLanguage('en-US')">
                            <span class="language-flag">🇺🇸</span>
                            <span>English</span>
                        </button>
                        <button class="language-option" onclick="changeLanguage('ja-JP')">
                            <span class="language-flag">🇯🇵</span>
                            <span>日本語</span>
                        </button>
                        <button class="language-option" onclick="changeLanguage('ko-KR')">
                            <span class="language-flag">🇰🇷</span>
                            <span>한국어</span>
                        </button>
                    </div>
                </div>

                <!-- 主题切换器 -->
                <div class="theme-switcher tooltip" data-tooltip="切换主题">
                    <i class="bi bi-sun-fill theme-icon" id="light-icon"></i>
                    <button class="theme-toggle" id="theme-toggle" onclick="toggleTheme()">
                    </button>
                    <i class="bi bi-moon-fill theme-icon" id="dark-icon"></i>
                </div>

                <!-- 设置按钮 -->
                <button class="btn btn-ghost btn-circle tooltip" data-tooltip="设置" onclick="toggleSettings()">
                    <i class="bi bi-gear"></i>
                </button>

                <!-- 快捷键帮助 -->
                <button class="btn btn-ghost btn-circle tooltip" data-tooltip="快捷键 (Ctrl+?)" onclick="toggleShortcuts()">
                    <i class="bi bi-question-circle"></i>
                </button>

                <!-- 用户菜单 -->
                <button class="user-menu" onclick="toggleUserMenu()">
                    <div class="user-avatar" id="user-avatar">A</div>
                    <span id="nav-username" data-i18n="nav.user">管理员</span>
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
        </nav>

        <!-- 主对话区域 -->
        <div class="chat-main">
            <div class="chat-area">
                <!-- 对话头部 -->
                <div class="chat-header">
                    <div class="chat-header-left">
                        <div class="chat-title">AI运维助手</div>
                        <div class="chat-subtitle">通过自然语言对话管理您的IT基础设施</div>
                    </div>
                    <div class="chat-header-right">
                        <div class="chat-controls">
                            <button class="btn btn-secondary btn-sm" onclick="showChatHistory()">
                                <i class="bi bi-clock-history"></i>
                                对话历史
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="startNewChat()">
                                <i class="bi bi-plus-circle"></i>
                                新建对话
                            </button>
                        </div>
                        <div class="session-info" id="current-session-info">
                            <small>当前对话：<span id="current-session-title">新对话</span></small>
                        </div>
                    </div>
                </div>

                <!-- 对话消息区域 -->
                <div class="chat-messages" id="chat-messages">
                    <!-- 欢迎界面 -->
                    <div class="welcome-screen" id="welcome-screen">
                        <div class="welcome-avatar">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div class="welcome-title">您好！我是您的AI运维助手</div>
                        <div class="welcome-description">
                            我可以帮您管理服务器、监控系统状态、处理告警等。请告诉我您需要什么帮助？
                        </div>

                        <!-- 功能介绍卡片 -->
                        <div class="feature-cards">
                            <div class="feature-card" onclick="insertSuggestion('查看所有主机状态')">
                                <div class="feature-icon">
                                    <i class="bi bi-server"></i>
                                </div>
                                <div class="feature-title">主机管理</div>
                                <div class="feature-desc">查看服务器状态、执行命令、管理主机</div>
                                <div class="feature-example">例如："查看所有主机状态"</div>
                            </div>
                            <div class="feature-card" onclick="insertSuggestion('显示最近的告警信息')">
                                <div class="feature-icon">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="feature-title">监控告警</div>
                                <div class="feature-desc">实时监控、告警处理、状态检查</div>
                                <div class="feature-example">例如："显示最近的告警信息"</div>
                            </div>
                            <div class="feature-card" onclick="insertSuggestion('生成今天的运维统计报表')">
                                <div class="feature-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="feature-title">统计报表</div>
                                <div class="feature-desc">数据分析、性能统计、趋势报告</div>
                                <div class="feature-example">例如："生成今天的运维统计报表"</div>
                            </div>
                            <div class="feature-card" onclick="insertSuggestion('在web-server-01上执行 ps aux')">
                                <div class="feature-icon">
                                    <i class="bi bi-terminal"></i>
                                </div>
                                <div class="feature-title">命令执行</div>
                                <div class="feature-desc">远程执行命令、脚本运行、系统操作</div>
                                <div class="feature-example">例如："在web-server-01上执行 ps aux"</div>
                            </div>
                        </div>

                        <!-- 快捷操作按钮 -->
                        <div class="quick-actions">
                            <button class="quick-action-btn" onclick="quickAction('查看所有主机状态')">
                                <div class="quick-action-icon">
                                    <i class="bi bi-server"></i>
                                </div>
                                <div class="quick-action-text">查看主机</div>
                            </button>
                            <button class="quick-action-btn" onclick="quickAction('检查系统告警')">
                                <div class="quick-action-icon">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="quick-action-text">检查告警</div>
                            </button>
                            <button class="quick-action-btn" onclick="quickAction('显示系统状态')">
                                <div class="quick-action-icon">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div class="quick-action-text">系统状态</div>
                            </button>
                            <button class="quick-action-btn" onclick="quickAction('生成性能统计报表')">
                                <div class="quick-action-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="quick-action-text">性能统计</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 对话输入区域 -->
                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <div class="input-wrapper">
                            <input
                                type="text"
                                class="chat-input"
                                id="chat-input"
                                placeholder="输入您的问题，例如：查看主机状态、检查告警信息..."
                                onkeypress="handleChatKeyPress(event)"
                                autocomplete="off"
                            >
                            <button class="send-button" onclick="sendMessage()" id="send-button">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>

                        <!-- 输入建议 -->
                        <div class="input-suggestions">
                            <div class="suggestion-label">建议：</div>
                            <button class="suggestion-btn" onclick="insertSuggestion('查看所有主机状态')">
                                <span>查看主机状态</span>
                            </button>
                            <button class="suggestion-btn" onclick="insertSuggestion('检查系统告警')">
                                <span>检查告警</span>
                            </button>
                            <button class="suggestion-btn" onclick="insertSuggestion('显示性能统计')">
                                <span>性能统计</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对话历史侧边栏 -->
        <div class="chat-history-sidebar" id="chat-history-sidebar">
            <div class="sidebar-header">
                <h5 class="mb-0">对话历史</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="hideChatHistory()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="sidebar-content">
                <div class="search-box mb-3">
                    <input type="text" class="form-control form-control-sm" placeholder="搜索对话..." id="chat-search">
                </div>
                <div class="chat-sessions" id="chat-sessions">
                    <div class="loading-sessions text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2">加载对话历史...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 粒子背景效果 -->
        <div class="particles-container" id="particles-container"></div>

        <!-- 设置面板 -->
        <div class="settings-panel" id="settings-panel">
            <div class="settings-header">
                <div class="settings-title">个性化设置</div>
                <div class="settings-subtitle">自定义您的AI运维助手体验</div>
                <button class="btn btn-ghost btn-circle" style="position: absolute; top: 1rem; right: 1rem;" onclick="toggleSettings()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="settings-content">
                <div class="setting-group">
                    <div class="setting-group-title">
                        <i class="bi bi-palette"></i>
                        外观设置
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-label">主题模式</div>
                            <div class="setting-description">选择亮色或暗色主题</div>
                        </div>
                        <div class="setting-control">
                            <div class="theme-switcher">
                                <i class="bi bi-sun-fill theme-icon"></i>
                                <button class="theme-toggle" onclick="toggleTheme()"></button>
                                <i class="bi bi-moon-fill theme-icon"></i>
                            </div>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-label">动画效果</div>
                            <div class="setting-description">启用或禁用界面动画</div>
                        </div>
                        <div class="setting-control">
                            <label class="switch">
                                <input type="checkbox" id="animations-toggle" checked onchange="toggleAnimations()">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-label">粒子效果</div>
                            <div class="setting-description">显示背景粒子动画</div>
                        </div>
                        <div class="setting-control">
                            <label class="switch">
                                <input type="checkbox" id="particles-toggle" checked onchange="toggleParticles()">
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="setting-group">
                    <div class="setting-group-title">
                        <i class="bi bi-chat-dots"></i>
                        对话设置
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-label">自动保存对话</div>
                            <div class="setting-description">自动保存对话历史到本地</div>
                        </div>
                        <div class="setting-control">
                            <label class="switch">
                                <input type="checkbox" id="auto-save-toggle" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-label">发送快捷键</div>
                            <div class="setting-description">选择发送消息的快捷键</div>
                        </div>
                        <div class="setting-control">
                            <select class="form-control form-control-sm" id="send-shortcut">
                                <option value="enter">Enter</option>
                                <option value="ctrl-enter">Ctrl + Enter</option>
                                <option value="shift-enter">Shift + Enter</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷键帮助面板 -->
        <div class="shortcuts-panel" id="shortcuts-panel">
            <div class="shortcuts-title">快捷键帮助</div>
            <div class="shortcuts-group">
                <div class="shortcuts-group-title">对话操作</div>
                <div class="shortcut-item">
                    <div class="shortcut-description">发送消息</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Enter</span>
                    </div>
                </div>
                <div class="shortcut-item">
                    <div class="shortcut-description">新建对话</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Ctrl</span>
                        <span>+</span>
                        <span class="key">N</span>
                    </div>
                </div>
                <div class="shortcut-item">
                    <div class="shortcut-description">对话历史</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Ctrl</span>
                        <span>+</span>
                        <span class="key">H</span>
                    </div>
                </div>
            </div>
            <div class="shortcuts-group">
                <div class="shortcuts-group-title">界面操作</div>
                <div class="shortcut-item">
                    <div class="shortcut-description">切换主题</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Ctrl</span>
                        <span>+</span>
                        <span class="key">T</span>
                    </div>
                </div>
                <div class="shortcut-item">
                    <div class="shortcut-description">打开设置</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Ctrl</span>
                        <span>+</span>
                        <span class="key">,</span>
                    </div>
                </div>
                <div class="shortcut-item">
                    <div class="shortcut-description">显示快捷键</div>
                    <div class="keyboard-shortcut">
                        <span class="key">Ctrl</span>
                        <span>+</span>
                        <span class="key">?</span>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <button class="btn btn-primary" onclick="toggleShortcuts()">关闭</button>
            </div>
        </div>

        <!-- 模态框遮罩 -->
        <div class="modal-overlay" id="modal-overlay" onclick="closeAllModals()"></div>
    </div>

    <!-- 国际化支持 -->
    <script src="/static/js/i18n.js"></script>

    <!-- 工作流支持 -->
    <script src="/static/js/workflow.js"></script>

    <!-- JavaScript -->
    <script>
        // 全局变量
        let currentUser = null;
        let chatMessages = [];
        let isTyping = false;
        let currentSessionId = null;
        let currentSessionTitle = '新对话';
        let chatSessions = [];

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadChatSessions();
            initializeChat();
        });

        // 初始化聊天功能
        function initializeChat() {
            console.log('Initializing chat...');
            
            // 如果没有当前会话ID，创建一个新的
            if (!currentSessionId) {
                currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                currentSessionTitle = '新对话';
                chatMessages = [];
            }
            
            updateSessionInfo();
            showWelcomeScreen();
            
            console.log('Chat initialized with session:', currentSessionId);
        }

        function showWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'block';
            }
        }

        function hideWelcomeScreen() {
            const welcomeScreen = document.getElementById('welcome-screen');
            if (welcomeScreen) {
                welcomeScreen.style.display = 'none';
            }
        }

        function clearChat() {
            const messagesContainer = document.getElementById('chat-messages');
            if (messagesContainer) {
                // 清空所有消息，但保留欢迎界面
                const welcomeScreen = messagesContainer.querySelector('#welcome-screen');
                messagesContainer.innerHTML = '';
                if (welcomeScreen) {
                    messagesContainer.appendChild(welcomeScreen);
                }
            }
            chatMessages = [];
        }

        function updateSessionInfo() {
            const sessionInfo = document.getElementById('current-session-title');
            if (sessionInfo) {
                sessionInfo.textContent = currentSessionTitle;
            }
        }

        function loadChatSessions() {
            chatSessions = JSON.parse(localStorage.getItem('chatSessions') || '[]');
            return chatSessions;
        }

        function showChatHistory() {
            const sidebar = document.getElementById('chat-history-sidebar');
            sidebar.classList.add('show');
            loadChatSessions();
            renderChatSessions();
        }

        function hideChatHistory() {
            const sidebar = document.getElementById('chat-history-sidebar');
            sidebar.classList.remove('show');
        }

        function renderChatSessions() {
            const container = document.getElementById('chat-sessions');

            if (chatSessions.length === 0) {
                container.innerHTML = `
                    <div class="no-sessions text-center py-4">
                        <i class="bi bi-chat-dots text-muted" style="font-size: 2rem;"></i>
                        <div class="mt-2 text-muted">暂无对话历史</div>
                        <button class="btn btn-sm btn-primary mt-2" onclick="hideChatHistory(); startNewChat();">
                            开始新对话
                        </button>
                    </div>
                `;
                return;
            }

            const sessionsHtml = chatSessions.map(session => `
                <div class="session-item ${session.id === currentSessionId ? 'active' : ''}"
                     onclick="loadSession('${session.id}')">
                    <div class="session-title">${session.title}</div>
                    <div class="session-meta">
                        <small class="text-muted">${formatDate(session.updatedAt)}</small>
                        <small class="text-muted">${session.messages.length} 条消息</small>
                    </div>
                    <div class="session-actions">
                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); deleteSession('${session.id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = sessionsHtml;
        }

        function startNewChat() {
            // 保存当前对话（如果有消息）
            const messages = document.getElementById('chat-messages');
            if (messages && messages.children.length > 1) { // 除了欢迎界面还有其他消息
                saveCurrentSession();
            }

            // 清空当前对话
            clearChat();

            // 重置会话ID
            currentSessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            currentSessionTitle = '新对话';

            // 更新UI
            updateSessionInfo();
            showWelcomeScreen();

            console.log('Started new chat with session:', currentSessionId);
        }

        function loadSession(sessionId) {
            // 保存当前会话
            if (currentSessionId && currentSessionId !== sessionId) {
                saveCurrentSession();
            }

            const session = chatSessions.find(s => s.id === sessionId);
            if (!session) return;

            // 清空当前对话
            clearChat();

            // 设置当前会话
            currentSessionId = sessionId;
            currentSessionTitle = session.title;

            // 更新UI
            updateSessionInfo();
            hideWelcomeScreen();

            // 加载消息
            session.messages.forEach(msg => {
                addMessage(msg.sender, msg.content, false, false);
            });

            // 隐藏侧边栏
            hideChatHistory();
        }

        function deleteSession(sessionId) {
            if (confirm('确定要删除这个对话吗？')) {
                chatSessions = chatSessions.filter(s => s.id !== sessionId);
                localStorage.setItem('chatSessions', JSON.stringify(chatSessions));

                if (currentSessionId === sessionId) {
                    startNewChat();
                }

                renderChatSessions();
            }
        }

        function saveCurrentSession() {
            if (!currentSessionId || chatMessages.length === 0) return;

            const sessionIndex = chatSessions.findIndex(s => s.id === currentSessionId);
            const sessionData = {
                id: currentSessionId,
                title: currentSessionTitle,
                messages: chatMessages,
                updatedAt: new Date().toISOString(),
                lastMessage: chatMessages.length > 0 ? chatMessages[chatMessages.length - 1].content.substring(0, 50) + '...' : ''
            };

            if (sessionIndex >= 0) {
                chatSessions[sessionIndex] = sessionData;
            } else {
                chatSessions.unshift(sessionData);
            }

            // 限制历史记录数量
            if (chatSessions.length > 50) {
                chatSessions = chatSessions.slice(0, 50);
            }

            localStorage.setItem('chatSessions', JSON.stringify(chatSessions));
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return '刚刚';
            if (diffMins < 60) return `${diffMins}分钟前`;
            if (diffHours < 24) return `${diffHours}小时前`;
            if (diffDays < 7) return `${diffDays}天前`;
            return date.toLocaleDateString('zh-CN');
        }

        // 消息处理函数
        function handleChatKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 全局变量用于管理等待状态
        let typingTimeout = null;
        let isWaitingForResponse = false;

        function sendMessage() {
            const input = document.getElementById('chat-input');
            const message = input.value.trim();

            if (!message) return;

            // 防止重复发送
            if (isWaitingForResponse) {
                showStatusMessage('正在等待响应，请稍候...', 'warning');
                return;
            }

            // 隐藏欢迎界面
            hideWelcomeScreen();

            // 添加用户消息
            addMessage('user', message, true);

            // 清空输入框
            input.value = '';

            // 设置等待状态
            isWaitingForResponse = true;

            // 显示AI正在输入
            showTypingIndicator();

            // 设置超时机制（30秒）
            typingTimeout = setTimeout(() => {
                hideTypingIndicator();
                showErrorMessage('AI响应超时，请重试');
                isWaitingForResponse = false;
            }, 30000);

            // 发送到后端
            sendToBackend(message);
        }

        // 格式化消息内容，支持换行和基本的Markdown
        function formatMessageContent(content) {
            // 转义HTML特殊字符
            const escapeHtml = (text) => {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            // 处理换行
            let formatted = escapeHtml(content).replace(/\n/g, '<br>');

            // 处理代码块
            formatted = formatted.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
                return `<div class="code-block"><pre><code class="language-${lang || 'text'}">${code.trim()}</code></pre></div>`;
            });

            // 处理行内代码
            formatted = formatted.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');

            // 处理粗体
            formatted = formatted.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

            // 处理斜体
            formatted = formatted.replace(/\*([^*]+)\*/g, '<em>$1</em>');

            // 处理表情符号和图标
            formatted = formatted.replace(/🔧|📊|📈|✅|💻|⚠️|❌|🚀|🔍|📋/g, '<span class="emoji">$&</span>');

            return formatted;
        }

        function addMessage(sender, content, animate = true, saveToHistory = true) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            if (animate) {
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(20px)';
            }

            const avatarDiv = document.createElement('div');
            avatarDiv.className = 'message-avatar';
            avatarDiv.textContent = sender === 'user' ? 'U' : 'AI';

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';

            // 格式化内容，支持换行和基本的Markdown
            const formattedContent = formatMessageContent(content);
            contentDiv.innerHTML = formattedContent;

            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeDiv);

            messagesContainer.appendChild(messageDiv);

            if (animate) {
                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease-out';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 50);
            }

            // 滚动到底部
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 保存到历史记录
            if (saveToHistory) {
                chatMessages.push({
                    sender: sender,
                    content: content,
                    timestamp: new Date().toISOString()
                });

                // 自动保存会话
                if (chatMessages.length % 5 === 0) {
                    saveCurrentSession();
                }
            }
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chat-messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message assistant typing-indicator';
            typingDiv.id = 'typing-indicator';

            typingDiv.innerHTML = `
                <div class="message-avatar">AI</div>
                <div class="typing-indicator">
                    <span class="typing-text">AI正在思考</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }

            // 清理等待状态
            isWaitingForResponse = false;
            if (typingTimeout) {
                clearTimeout(typingTimeout);
                typingTimeout = null;
            }
        }

        // 显示状态消息
        function showStatusMessage(message, type = 'info') {
            const messagesContainer = document.getElementById('chat-messages');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message ${type}`;
            statusDiv.innerHTML = `
                <div class="status-content">
                    <i class="bi bi-info-circle"></i>
                    <span>${message}</span>
                </div>
            `;

            messagesContainer.appendChild(statusDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // 3秒后自动移除
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.remove();
                }
            }, 3000);
        }

        // 显示错误消息
        function showErrorMessage(message) {
            const messagesContainer = document.getElementById('chat-messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'message assistant error';
            errorDiv.innerHTML = `
                <div class="message-avatar">⚠️</div>
                <div class="message-content">
                    <div class="error-content">
                        <strong>系统提示：</strong>${message}
                    </div>
                </div>
            `;

            messagesContainer.appendChild(errorDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // WebSocket连接管理
        let ws = null;
        let wsConnected = false;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        // 建立WebSocket连接
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/chat?session_id=${currentSessionId}`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function(event) {
                    console.log('WebSocket连接已建立');
                    wsConnected = true;
                    reconnectAttempts = 0;
                    updateConnectionStatus(true);
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        handleWebSocketMessage(data);
                    } catch (error) {
                        console.error('解析WebSocket消息失败:', error);
                    }
                };

                ws.onclose = function(event) {
                    console.log('WebSocket连接已关闭');
                    wsConnected = false;
                    updateConnectionStatus(false);

                    // 尝试重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        setTimeout(() => {
                            console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                            connectWebSocket();
                        }, 2000 * reconnectAttempts);
                    }
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    wsConnected = false;
                    updateConnectionStatus(false);
                };

            } catch (error) {
                console.error('创建WebSocket连接失败:', error);
                // 如果WebSocket连接失败，回退到模拟响应
                wsConnected = false;
                updateConnectionStatus(false);
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(data) {
            hideTypingIndicator();

            switch (data.type) {
                case 'connected':
                    console.log('WebSocket连接确认:', data.data);
                    break;

                case 'user_message':
                    // 用户消息确认，通常不需要处理
                    break;

                case 'assistant_message':
                    // AI助手响应
                    if (data.data && data.data.content) {
                        addMessage('assistant', data.data.content, true);

                        // 更新会话标题
                        if (currentSessionTitle === '新对话' && chatMessages.length <= 2) {
                            const lastUserMessage = chatMessages.find(m => m.type === 'user');
                            if (lastUserMessage) {
                                currentSessionTitle = lastUserMessage.content.substring(0, 20) +
                                    (lastUserMessage.content.length > 20 ? '...' : '');
                                updateSessionInfo();
                            }
                        }
                    }
                    break;

                case 'error':
                    console.error('AI处理错误:', data.message);
                    addMessage('assistant', '抱歉，我遇到了一些问题，请稍后再试。', true);
                    break;

                case 'pong':
                    // 心跳响应
                    break;

                default:
                    console.log('未知消息类型:', data.type);
            }
        }

        // 发送WebSocket消息
        function sendWebSocketMessage(message) {
            if (ws && wsConnected) {
                const data = {
                    type: 'message',
                    content: message
                };
                ws.send(JSON.stringify(data));
                return true;
            }
            return false;
        }

        // 更新连接状态显示
        function updateConnectionStatus(connected) {
            const statusElement = document.querySelector('.connection-status');
            if (statusElement) {
                statusElement.textContent = connected ? '已连接' : '连接中断';
                statusElement.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
            }
        }

        // 发送消息到后端（优先使用WebSocket，失败时回退到模拟）
        function sendToBackend(message) {
            try {
                // 尝试通过WebSocket发送
                if (sendWebSocketMessage(message)) {
                    console.log('消息已通过WebSocket发送');
                    showStatusMessage('消息已发送，等待AI响应...', 'info');
                    return;
                }

                // WebSocket不可用时，使用模拟响应
                console.log('WebSocket不可用，使用模拟响应');
                showStatusMessage('WebSocket连接不可用，使用模拟响应...', 'warning');
                simulateAIResponse(message);
            } catch (error) {
                console.error('发送消息失败:', error);
                hideTypingIndicator();
                showErrorMessage('发送消息失败，请检查网络连接后重试');
            }
        }

        // 模拟AI响应（WebSocket连接失败时的备用方案）
        function simulateAIResponse(message) {
            setTimeout(() => {
                hideTypingIndicator();

                let aiResponse = '';
                if (message.includes('主机状态') || message.includes('服务器')) {
                    aiResponse = `正在查询主机状态...\n\n📊 **系统状态概览**\n\n🟢 **web-server-01**: 运行正常\n- CPU使用率: 15%\n- 内存使用率: 45%\n- 磁盘使用率: 60%\n\n🟢 **db-server-01**: 运行正常\n- CPU使用率: 8%\n- 内存使用率: 70%\n- 磁盘使用率: 40%\n\n🟡 **cache-server-01**: 需要关注\n- CPU使用率: 85%\n- 内存使用率: 90%\n- 磁盘使用率: 30%\n\n所有主机网络连接正常，建议关注cache-server-01的资源使用情况。`;
                } else if (message.includes('告警') || message.includes('警报')) {
                    aiResponse = `正在检查系统告警...\n\n🚨 **当前告警信息**\n\n⚠️ **高优先级告警 (1条)**\n- cache-server-01: CPU使用率超过80% (当前85%)\n- 时间: 2025-01-25 08:45:32\n- 持续时间: 15分钟\n\n💡 **建议操作**\n1. 检查cache-server-01上的进程\n2. 考虑重启缓存服务\n3. 监控后续状态变化\n\n其他系统运行正常，无其他告警信息。`;
                } else if (message.includes('统计') || message.includes('报表')) {
                    aiResponse = `正在生成统计报表...\n\n📈 **今日运维统计报表**\n\n**系统概况**\n- 监控主机数量: 3台\n- 正常运行: 2台\n- 需要关注: 1台\n- 平均响应时间: 120ms\n\n**告警统计**\n- 今日告警总数: 3条\n- 已处理: 2条\n- 待处理: 1条\n- 告警解决率: 67%\n\n**性能指标**\n- 系统平均负载: 0.65\n- 网络吞吐量: 850 Mbps\n- 存储使用率: 45%\n\n报表已生成，建议重点关注cache-server-01的性能优化。`;
                } else if (message.includes('执行') || message.includes('命令')) {
                    aiResponse = `正在执行命令...\n\n💻 **命令执行结果**\n\n\`\`\`bash\n$ ps aux\nUSER       PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND\nroot         1  0.0  0.1  19356  1544 ?        Ss   Jan24   0:01 /sbin/init\nroot         2  0.0  0.0      0     0 ?        S    Jan24   0:00 [kthreadd]\nwww-data  1234  2.1  5.2 123456 12345 ?        S    08:30   0:15 nginx: worker\nmysql     5678  1.8  15.3 456789 45678 ?        Sl   08:00   1:23 mysqld\nredis     9012  0.5  2.1  98765  9876 ?        S    08:00   0:08 redis-server\n\`\`\`\n\n✅ 命令执行成功！发现nginx、mysql、redis等关键服务正在正常运行。`;
                } else {
                    aiResponse = `我是您的AI运维助手，我已经收到您的消息："${message}"\n\n我可以帮助您：\n\n🔧 **主机管理**\n- 查看服务器状态\n- 执行系统命令\n- 监控资源使用\n\n📊 **监控告警**\n- 检查系统告警\n- 分析性能指标\n- 故障诊断建议\n\n📈 **统计报表**\n- 生成运维报表\n- 性能趋势分析\n- 系统健康评估\n\n请告诉我您具体需要什么帮助？`;
                }

                addMessage('assistant', aiResponse, true);

                // 更新会话标题（如果是新会话的第一条消息）
                if (currentSessionTitle === '新对话' && chatMessages.length <= 2) {
                    currentSessionTitle = message.substring(0, 20) + (message.length > 20 ? '...' : '');
                    updateSessionInfo();
                }
            }, 1500 + Math.random() * 1000);
        }

        // 添加连接状态指示器
        function addConnectionStatusIndicator() {
            const chatHeader = document.querySelector('.chat-header-right');
            if (chatHeader && !document.querySelector('.connection-status')) {
                const statusDiv = document.createElement('div');
                statusDiv.className = 'connection-status disconnected';
                statusDiv.textContent = '连接中...';
                statusDiv.style.cssText = `
                    font-size: 12px;
                    padding: 4px 8px;
                    border-radius: 12px;
                    margin-left: 10px;
                    transition: all 0.3s ease;
                `;
                chatHeader.appendChild(statusDiv);

                // 添加CSS样式
                const style = document.createElement('style');
                style.textContent = `
                    .connection-status.connected {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                    }
                    .connection-status.disconnected {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                    }
                `;
                document.head.appendChild(style);
            }
        }

        // 快捷操作函数
        function insertSuggestion(text) {
            const input = document.getElementById('chat-input');
            input.value = text;
            input.focus();
        }

        function quickAction(action) {
            insertSuggestion(action);
            sendMessage();
        }

        function toggleUserMenu() {
            // 用户菜单功能（可以后续扩展）
            console.log('Toggle user menu');
        }

        // ========================================
        // 主题系统功能
        // ========================================

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // 添加过渡效果
            document.documentElement.classList.add('theme-transition');

            // 切换主题
            document.documentElement.setAttribute('data-theme', newTheme);

            // 更新切换器状态
            const toggle = document.getElementById('theme-toggle');
            const lightIcon = document.getElementById('light-icon');
            const darkIcon = document.getElementById('dark-icon');

            if (newTheme === 'dark') {
                toggle.classList.add('dark');
                lightIcon.classList.remove('active');
                darkIcon.classList.add('active');
            } else {
                toggle.classList.remove('dark');
                lightIcon.classList.add('active');
                darkIcon.classList.remove('active');
            }

            // 保存用户偏好
            localStorage.setItem('theme', newTheme);

            // 移除过渡效果
            setTimeout(() => {
                document.documentElement.classList.remove('theme-transition');
            }, 500);
        }

        function initializeTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const toggle = document.getElementById('theme-toggle');
            const lightIcon = document.getElementById('light-icon');
            const darkIcon = document.getElementById('dark-icon');

            if (savedTheme === 'dark') {
                toggle.classList.add('dark');
                lightIcon.classList.remove('active');
                darkIcon.classList.add('active');
            } else {
                lightIcon.classList.add('active');
                darkIcon.classList.remove('active');
            }
        }

        // ========================================
        // 设置面板功能
        // ========================================

        function toggleSettings() {
            const panel = document.getElementById('settings-panel');
            const overlay = document.getElementById('modal-overlay');

            if (panel.classList.contains('show')) {
                panel.classList.remove('show');
                overlay.classList.remove('show');
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function toggleShortcuts() {
            const panel = document.getElementById('shortcuts-panel');
            const overlay = document.getElementById('modal-overlay');

            if (panel.classList.contains('show')) {
                panel.classList.remove('show');
                overlay.classList.remove('show');
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function closeAllModals() {
            const settingsPanel = document.getElementById('settings-panel');
            const shortcutsPanel = document.getElementById('shortcuts-panel');
            const overlay = document.getElementById('modal-overlay');

            settingsPanel.classList.remove('show');
            shortcutsPanel.classList.remove('show');
            overlay.classList.remove('show');
        }

        function toggleAnimations() {
            const enabled = document.getElementById('animations-toggle').checked;
            if (enabled) {
                document.documentElement.style.removeProperty('--duration-75');
                document.documentElement.style.removeProperty('--duration-100');
                document.documentElement.style.removeProperty('--duration-150');
                document.documentElement.style.removeProperty('--duration-200');
                document.documentElement.style.removeProperty('--duration-300');
                document.documentElement.style.removeProperty('--duration-500');
                document.documentElement.style.removeProperty('--duration-700');
                document.documentElement.style.removeProperty('--duration-1000');
            } else {
                document.documentElement.style.setProperty('--duration-75', '0ms');
                document.documentElement.style.setProperty('--duration-100', '0ms');
                document.documentElement.style.setProperty('--duration-150', '0ms');
                document.documentElement.style.setProperty('--duration-200', '0ms');
                document.documentElement.style.setProperty('--duration-300', '0ms');
                document.documentElement.style.setProperty('--duration-500', '0ms');
                document.documentElement.style.setProperty('--duration-700', '0ms');
                document.documentElement.style.setProperty('--duration-1000', '0ms');
            }
            localStorage.setItem('animations-enabled', enabled);
        }

        function toggleParticles() {
            const enabled = document.getElementById('particles-toggle').checked;
            const container = document.getElementById('particles-container');

            if (enabled) {
                container.style.display = 'block';
                initializeParticles();
            } else {
                container.style.display = 'none';
            }
            localStorage.setItem('particles-enabled', enabled);
        }

        // ========================================
        // 粒子效果
        // ========================================

        function initializeParticles() {
            const container = document.getElementById('particles-container');
            const particleCount = 50;

            // 清空现有粒子
            container.innerHTML = '';

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // 随机位置和延迟
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 20) + 's';

                container.appendChild(particle);
            }
        }

        // ========================================
        // 快捷键支持
        // ========================================

        function initializeKeyboardShortcuts() {
            document.addEventListener('keydown', function(event) {
                // Ctrl + T: 切换主题
                if (event.ctrlKey && event.key === 't') {
                    event.preventDefault();
                    toggleTheme();
                }

                // Ctrl + ,: 打开设置
                if (event.ctrlKey && event.key === ',') {
                    event.preventDefault();
                    toggleSettings();
                }

                // Ctrl + ?: 显示快捷键帮助
                if (event.ctrlKey && event.key === '?') {
                    event.preventDefault();
                    toggleShortcuts();
                }

                // Ctrl + N: 新建对话
                if (event.ctrlKey && event.key === 'n') {
                    event.preventDefault();
                    startNewChat();
                }

                // Ctrl + H: 对话历史
                if (event.ctrlKey && event.key === 'h') {
                    event.preventDefault();
                    showChatHistory();
                }

                // Escape: 关闭模态框
                if (event.key === 'Escape') {
                    closeAllModals();
                }
            });
        }

        // ========================================
        // 语言切换功能
        // ========================================

        function toggleLanguageMenu() {
            const dropdown = document.getElementById('language-dropdown');
            dropdown.classList.toggle('show');
        }

        function changeLanguage(langCode) {
            if (window.i18n) {
                window.i18n.changeLanguage(langCode).then(() => {
                    updateLanguageDisplay(langCode);
                    toggleLanguageMenu(); // 关闭下拉菜单
                });
            }
        }

        function updateLanguageDisplay(langCode) {
            const flagElement = document.getElementById('current-language-flag');
            const codeElement = document.getElementById('current-language-code');
            const options = document.querySelectorAll('.language-option');

            // 更新当前语言显示
            const languageMap = {
                'zh-CN': { flag: '🇨🇳', name: '中文' },
                'en-US': { flag: '🇺🇸', name: 'English' },
                'ja-JP': { flag: '🇯🇵', name: '日本語' },
                'ko-KR': { flag: '🇰🇷', name: '한국어' }
            };

            if (languageMap[langCode]) {
                flagElement.textContent = languageMap[langCode].flag;
                codeElement.textContent = languageMap[langCode].name;
            }

            // 更新选项状态
            options.forEach(option => {
                option.classList.remove('active');
                if (option.onclick.toString().includes(langCode)) {
                    option.classList.add('active');
                }
            });
        }

        // ========================================
        // 可访问性功能
        // ========================================

        function announceToScreenReader(message) {
            const announcer = document.getElementById('status-announcer');
            announcer.textContent = message;

            // 清空后重新设置，确保屏幕阅读器能读取
            setTimeout(() => {
                announcer.textContent = '';
            }, 1000);
        }

        function initializeAccessibility() {
            // 检测键盘用户
            document.addEventListener('keydown', function() {
                document.body.classList.add('keyboard-user');
            });

            document.addEventListener('mousedown', function() {
                document.body.classList.remove('keyboard-user');
            });

            // 检测减少动画偏好
            if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
                document.body.classList.add('reduced-motion');
            }

            // 检测高对比度偏好
            if (window.matchMedia('(prefers-contrast: high)').matches) {
                document.body.classList.add('high-contrast');
            }
        }

        function hidePageLoader() {
            const loader = document.getElementById('page-loader');
            loader.classList.add('hidden');

            setTimeout(() => {
                loader.style.display = 'none';
            }, 500);
        }

        // ========================================
        // 初始化所有功能
        // ========================================

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化可访问性
            initializeAccessibility();

            // 原有初始化
            loadChatSessions();
            initializeChat();

            // 新增初始化
            initializeTheme();
            initializeKeyboardShortcuts();

            // 恢复用户设置
            const animationsEnabled = localStorage.getItem('animations-enabled') !== 'false';
            const particlesEnabled = localStorage.getItem('particles-enabled') !== 'false';

            document.getElementById('animations-toggle').checked = animationsEnabled;
            document.getElementById('particles-toggle').checked = particlesEnabled;

            if (!animationsEnabled) {
                toggleAnimations();
            }

            // 建立WebSocket连接
            connectWebSocket();

            // 添加连接状态显示
            addConnectionStatusIndicator();

            if (particlesEnabled) {
                initializeParticles();
            } else {
                document.getElementById('particles-container').style.display = 'none';
            }

            // 点击外部关闭语言菜单
            document.addEventListener('click', function(event) {
                const languageSwitcher = document.querySelector('.language-switcher');
                if (!languageSwitcher.contains(event.target)) {
                    document.getElementById('language-dropdown').classList.remove('show');
                }
            });

            // 模拟加载完成
            setTimeout(() => {
                hidePageLoader();
                announceToScreenReader('页面加载完成');
            }, 2000);
        });

        // 监听语言变更事件
        window.addEventListener('languageChanged', function(event) {
            announceToScreenReader(`语言已切换为 ${event.detail.language}`);
        });
    </script>
</body>
</html>
